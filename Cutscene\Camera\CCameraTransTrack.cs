using Cysharp.Text;
using Slate.ActionClips;
using UnityEngine;

namespace Slate
{
    [Name("C类编辑器_镜头轨道")]
    [Attachable(typeof(DirectorGroup))]
    [Category("C类编辑器")]

    public class CCameraTransTrack : DirectorActionTrack
    {
        [SerializeField]
        public int CamTemplateID = 0;
        protected override void OnCreate()
        {
            base.OnCreate();
            this.name = ZString.Format("C类编辑器_镜头轨道[{0}]", CamTemplateID);
        }

        protected override void OnSceneGUI()
        {
            base.OnSceneGUI();
            this.name = ZString.Format("C类编辑器_镜头轨道[{0}]", CamTemplateID);
        }

        protected override void OnEnter()
        {
            base.OnEnter();
        }
#if UNITY_EDITOR
        public override Texture icon
        {
            get
            {
                _icon = Resources.Load("PlayIcon") as Texture;
                return _icon as Texture;
            }
        }
#endif
        public override string info
        {
            get
            {
                return "camera trans track info";
            }
        }

        public CSingleCameraTransClip FindFirstCameraTransClip()
        {
            foreach (var clip in clips)
            {
                if (clip is CSingleCameraTransClip camClip)
                    return camClip;
            }

            return null;
        }
    }
}