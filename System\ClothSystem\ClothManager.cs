using System.Collections;
using System.Collections.Generic;
using MagicaCloth2;
using UnityEngine;
using System;
using System.IO;
using SimpleJSON;
using Cysharp.Text;
using System.Text.RegularExpressions;
using System.Linq;
using VGame.Framework.Tasks.Conditions;


namespace CEditor
{
    [Serializable]
    [ExecuteAlways]
    public class ClothClipSerializeData
    {
        [SerializeField, Tooltip("启用布料")]
        public bool enable = true;

        [SerializeField, Tooltip("布料名称")]
        public string clothName = "";

        [SerializeField, Tooltip("混合权重"), Range(0f, 1f)]
        public float blendWeight = 1.0f;

        [SerializeField, Tooltip("预设名称")]
        public string presetName = "";

        [SerializeField, Tooltip("在使用预设时是否强制后处理")]
        public bool ifPostSetting = true;

        public ClothSerializeData cdata = new();
    }
    
    [ExecuteAlways]
    public class ClothManager : MonoBehaviour
    {
        public List<ClothClipSerializeData> clothSerilizeData { get; set; } = new();
        public List<string> clothNames { get; set; } = new List<string>();

        private List<MagicaCloth> _clothComponents;

        private string charPresetDir = "Assets/ArtRes/Characters/MagicClothPreset";
        private string charCollidersDir = "Assets/ArtRes/Characters/MagicClothColliders";
        
        [Header("Sitting Detection Management")]
        [Tooltip("Enable sitting detection")]
        [SerializeField] private bool _enableSittingDetection = true;

        [Tooltip("Use child bone direction detection")]
        [SerializeField] private bool _useChildBoneDetection = true;

        [Tooltip("Angle threshold for sitting detection (degrees from vertical down)")]
        [SerializeField] private float _sittingAngleThreshold = 45.0f;

        [Tooltip("Minimum thigh bones required to trigger sitting")]
        [SerializeField] private int _minThighBonesForSitting = 1;

        [Header("Scene Collider Management")]
        [Tooltip("Enable scene collider management")]
        [SerializeField] private bool _enableDynamicColliderManagement = true;

        [Tooltip("Collider distance threshold")]
        [SerializeField] private float _colliderDistanceThreshold = 2.0f;

        [Tooltip("Update interval for distance check")]
        [SerializeField] private float _distanceCheckInterval = 2.0f;
        
        private static readonly string ThighBonePattern = @"^Bip\d+_[LR]_Thigh$";
        private Dictionary<string, Dictionary<string, Transform>> _thighBonesByPelvis;
        private Transform _rootTransform;
        [Header("Sitting State Display")]
        [SerializeField, Tooltip("Current sitting state (Read Only)")]
        private bool _isSitting = false;
        private bool _sittingDetectionInitialized = false;
        
        private List<ClothClipSerializeData> _savedClothState = null;
        private bool _hasBackupState = false;

        // for scene collider
        private List<ColliderComponent> _sceneColliders = new List<ColliderComponent>();
        private Dictionary<ColliderComponent, bool> _colliderActiveStates = new Dictionary<ColliderComponent, bool>();
        private float _lastDistanceCheckTime = 0f;
        private bool _dynamicColliderInitialized = false;

        public void Init()
        {
            if (_clothComponents == null)
            {
                _clothComponents = new List<MagicaCloth>(GetComponentsInChildren<MagicaCloth>(true));
            }
            if (_enableSittingDetection)
            {
                InitializeSittingDetection();
            }
            if (_enableDynamicColliderManagement)
            {
                ResetDynamicColliderManagement();
            }
        }

        public void Awake()
        {
            if (_clothComponents == null)
            {
                _clothComponents = new List<MagicaCloth>(GetComponentsInChildren<MagicaCloth>(true));
            }
            if (_enableSittingDetection)
            {
                InitializeSittingDetection();
            }
            if (_enableDynamicColliderManagement)
            {
                ResetDynamicColliderManagement();
            }
        }

        void Start()
        {
            if (_clothComponents == null)
            {
                _clothComponents = new List<MagicaCloth>(GetComponentsInChildren<MagicaCloth>(true));
            }

            if (clothNames.Count == 0)
            {
                foreach (var cloth in _clothComponents)
                {
                    if (cloth != null)
                    {
                        clothNames.Add(cloth.name);
                    }
                }
            }
            
            if (_enableSittingDetection)
            {
                InitializeSittingDetection();
            }
            if (_enableDynamicColliderManagement)
            {
                ResetDynamicColliderManagement();
            }
        }

        void Update()
        {
            if (_enableSittingDetection && _sittingDetectionInitialized)
            {
                UpdateSittingDetection();
            }

            if (_enableDynamicColliderManagement && _dynamicColliderInitialized)
            {
                UpdateDynamicColliderManagement();
            }
        }

        private void LateUpdate()
        {
        }

        /// <summary>
        /// Reset the scene simulation to its initial state.
        /// </summary>
        /// <param name="keepPose">If true, resume while maintaining posture.</param>
        public void ResetCharCloth(bool keepPose=false)
        {
            foreach (var cloth in _clothComponents)
            {
                cloth.ResetCloth(keepPose);
            }
        }

        public void SetClothEnabled(bool isEnabled)
        {
            if (_clothComponents == null)
                return;

            foreach (var cloth in _clothComponents)
            {
                if (cloth != null)
                {
                    cloth.enabled = isEnabled;
                }
            }
        }
        
        private void UpdateClothData(bool restore)
        {
            if (_clothComponents == null || _clothComponents.Count == 0 || clothSerilizeData == null || clothSerilizeData.Count == 0)
                return;

            foreach (var clothData in clothSerilizeData)
            {
                if (clothData == null || string.IsNullOrEmpty(clothData.clothName))
                    continue;

                var cloth = GetClothComponent(clothData.clothName);
                if (!cloth)
                    continue;

                var sdata = cloth.SerializeData;
                if (restore)
                {
                    JsonUtility.FromJsonOverwrite(JsonUtility.ToJson(clothData.cdata), sdata);
                }
                else if (clothData.presetName != "")
                {
                    string filePath = ZString.Concat(charPresetDir, "/", clothData.presetName, ".json");
                    LoadSinglePreset(this.gameObject, cloth, filePath, clothData.ifPostSetting);
                }
                sdata.blendWeight = Mathf.Clamp01(clothData.blendWeight);
                cloth.BuildAndRun();
            }
        }

        private MagicaCloth GetClothComponent(string clothName)
        {
            return _clothComponents?.Find(c => c && c.name == clothName);
        }
        
        public void SetClothManagerState(string charName, List<ClothClipSerializeData> inClothData, bool enable, float blendWeight, bool restore)
        {
            if (_clothComponents == null)
            {
                _clothComponents = new List<MagicaCloth>(GetComponentsInChildren<MagicaCloth>(true));
            }
            _clothComponents.ForEach(c => c.enabled = enable);

            if (blendWeight >= 0)
            {
                _clothComponents.ForEach(c => c.SerializeData.blendWeight = blendWeight);
            }

            if (inClothData != null && charName == name)
            {
                foreach (var item in inClothData)
                {
                    var cloth = _clothComponents.Find(c => c.name == item.clothName);

                    if (cloth)
                    {
                        cloth.enabled = item.enable;
                    }
                }
            }

            if (inClothData != null)
            {
                clothSerilizeData = inClothData;
                UpdateClothData(restore);
            }
        }

        private void SetClothWeight(string clothName, float targetWeight)
        {
            if (_clothComponents == null || string.IsNullOrEmpty(clothName))
                return;

            var cloth = GetClothComponent(clothName);
            if (cloth == null)
                return;

            targetWeight = Mathf.Clamp01(targetWeight);

            var sdata = cloth.SerializeData;
            sdata.blendWeight = targetWeight;

            var existingData = clothSerilizeData.Find(data => data.clothName == clothName);
            if (existingData != null)
            {
                existingData.blendWeight = targetWeight;
            }
            else
            {
                clothSerilizeData.Add(new ClothClipSerializeData
                {
                    clothName = clothName,
                    blendWeight = targetWeight,
                    enable = true,
                    presetName = "",
                    ifPostSetting = true
                });
            }
        }
        
        public void SetAllClothWeight(float weight)
        {
            if (_clothComponents == null)
                return;
            
            foreach (var cloth in _clothComponents)
            {
                if (cloth)
                {
                    var sdata = cloth.SerializeData;
                    sdata.blendWeight = weight;
                }
            }
        }
        
        public float GetCurrentClothWeight()
        {
            if (_clothComponents == null || _clothComponents.Count == 0)
                return 0f;

            var firstCloth = _clothComponents.Find(c => c != null);
            return firstCloth?.SerializeData.blendWeight ?? 0f;
        }

        public bool IsEnable()
        {
            if (_clothComponents == null || _clothComponents.Count == 0)
                return false;

            return _clothComponents.Exists(c => c != null && c.enabled);
        }

        public float GetBlendWeight()
        {
            if (_clothComponents == null || _clothComponents.Count == 0)
                return -1.0f;

            float? firstWeight = null;
            foreach (var cloth in _clothComponents)
            {
                if (cloth == null) continue;
                float weight = cloth.SerializeData.blendWeight;
                if (firstWeight == null)
                {
                    firstWeight = weight;
                }
                else if (!Mathf.Approximately(firstWeight.Value, weight))
                {
                    return -1.0f;
                }
            }
            return firstWeight ?? -1.0f;
        }
        
        public List<ClothClipSerializeData> GetCurrentClothConfigs()
        {
            var currentConfigs = new List<ClothClipSerializeData>();

            if (_clothComponents == null)
                return currentConfigs;

            foreach (var cloth in _clothComponents)
            {
                if (cloth == null) continue;

                var existingData = clothSerilizeData?.Find(data => data.clothName == cloth.name);
                var cdataCopy = new ClothSerializeData();
                JsonUtility.FromJsonOverwrite(JsonUtility.ToJson(cloth.SerializeData), cdataCopy);

                if (existingData != null)
                {
                    var configCopy = new ClothClipSerializeData
                    {
                        enable = cloth.enabled,
                        clothName = cloth.name,
                        blendWeight = cdataCopy.blendWeight,
                        presetName = existingData.presetName,
                        ifPostSetting = existingData.ifPostSetting,
                        cdata = cdataCopy
                    };
                    currentConfigs.Add(configCopy);
                }
                else
                {
                    var configCopy = new ClothClipSerializeData
                    {
                        enable = cloth.enabled,
                        clothName = cloth.name,
                        blendWeight = cdataCopy.blendWeight,
                        presetName = "",
                        ifPostSetting = true,
                        cdata = cdataCopy
                    };
                    currentConfigs.Add(configCopy);
                }
            }

            return currentConfigs;
        }

        public static void LoadSinglePreset(GameObject character, MagicaCloth cloth, string filePath, bool ifPostSetting = true)
        {
            if (cloth == null)
            {
                Debug.LogError("MagicaCloth component is null.");
                return;
            }
            
            string presetJson = File.ReadAllText(filePath);
            if (string.IsNullOrEmpty(presetJson))
            {
                Debug.LogWarning(ZString.Format("Preset file is empty or not found at path: {0}", filePath));
                return;
            }
        
            var sdata = cloth.SerializeData;
            if (sdata == null)
            {
                Debug.LogError(ZString.Format("SerializeData is null for {0}.", cloth.name));
                return;
            }
        
            try
            {
                var jsonObject = JSON.Parse(presetJson).AsObject;
        
                RestoreReferences(character, jsonObject);
        
                if (ifPostSetting)
                {
                    if (cloth.name.Contains("Hair_Back") || cloth.name.Contains("Hair_Front"))
                    {
                        if (jsonObject.HasKey("colliderCollisionConstraint"))
                        {
                            jsonObject["colliderCollisionConstraint"]["mode"] = (int)ColliderCollisionConstraint.Mode.Edge;
                        }
                    }
                    else if (cloth.name.Contains("Skirt"))
                    {
                        if (jsonObject.HasKey("colliderCollisionConstraint"))
                        {
                            jsonObject["colliderCollisionConstraint"]["mode"] = (int)ColliderCollisionConstraint.Mode.Edge;
                        }
                        jsonObject["connectionMode"] = (int)RenderSetupData.BoneConnectionMode.SequentialLoopMesh;
                    }
                    else if (cloth.name.Contains("Ribbon"))
                    {
                        if (jsonObject.HasKey("colliderCollisionConstraint"))
                        {
                            jsonObject["colliderCollisionConstraint"]["mode"] = (int)ColliderCollisionConstraint.Mode.Edge;
                        }
                    }
                    else if (cloth.name.Contains("Cloak"))
                    {
                        jsonObject["connectionMode"] = (int)RenderSetupData.BoneConnectionMode.SequentialNonLoopMesh;
                    }
        
                    // Apply global post settings
                    jsonObject["gravity"] = 4.0f;
                    jsonObject["gravityFalloff"] = 1.0f;
                    if (jsonObject.HasKey("inertiaConstraint"))
                    {
                        jsonObject["inertiaConstraint"]["movementInertiaSmoothing"] = 1.0f;
                    }
                    
                    if (jsonObject.HasKey("wind"))
                    {
                        jsonObject["wind"]["influence"] = 0.1f;
                    }
                }
        
                jsonObject.Remove("path");
                JsonUtility.FromJsonOverwrite(jsonObject.ToString(), sdata);
                cloth.BuildAndRun();
            }
            catch (System.Exception e)
            {
                Debug.LogError(ZString.Format("Failed to import preset for {0} from {1}: {2}", cloth.name, Path.GetFileName(filePath), e.Message));
            }
        }
        public static void RestoreReferences(GameObject character, SimpleJSON.JSONObject jsonObject)
        {
            if (jsonObject.HasKey("rootBones"))
            {
                var rootBones = jsonObject["rootBones"].AsArray;
                foreach (JSONNode bone in rootBones)
                {
                    if (bone.HasKey("path"))
                    {
                        string path = bone["path"];
                        Transform boneTransform = FindTransformByPath(character.transform, path);
                        if (boneTransform != null)
                        {
                            bone["instanceID"] = boneTransform.gameObject.transform.GetInstanceID();
                        }
                        else
                        {
                            Debug.LogError(ZString.Format("Bone not found at path: {0}", path));
                        }
                    }
                }
            }
        
            // Restore colliders using the path
            if (jsonObject.HasKey("colliderCollisionConstraint"))
            {
                var colliderCollisionConstraint = jsonObject["colliderCollisionConstraint"].AsObject;
                if (colliderCollisionConstraint.HasKey("colliderList"))
                {
                    var colliderList = colliderCollisionConstraint["colliderList"].AsArray;
                    foreach (JSONNode collider in colliderList)
                    {
                        if (collider.HasKey("path"))
                        {
                            string path = collider["path"];
                            Transform colliderTransform = FindTransformByPath(character.transform, path);
                            if (colliderTransform != null)
                            {
                                var colliderComponent = colliderTransform.GetComponent<ColliderComponent>();
                                if (colliderComponent != null)
                                {
                                    collider["instanceID"] = colliderComponent.GetInstanceID();
                                }
                                else
                                {
                                    Debug.LogError(ZString.Format("ColliderComponent not found on {0}", path));
                                }
                            }
                        }
                    }
                }
            }
        
            // Restore syncPartner using the path
            if (jsonObject.HasKey("syncPartner"))
            {
                var syncPartner = jsonObject["syncPartner"].AsObject;
                if (syncPartner.HasKey("path"))
                {
                    string path = syncPartner["path"];
                    Transform syncPartnerTransform = FindTransformByPath(character.transform, path);
                    if (syncPartnerTransform != null)
                    {
                        var magicaCloth = syncPartnerTransform.GetComponent<MagicaCloth>();
                        if (magicaCloth != null)
                        {
                            syncPartner["instanceID"] = magicaCloth.GetInstanceID();
                        }
                        else
                        {
                            Debug.LogError(ZString.Format("MagicaCloth component not found on {0}", path));
                        }
                    }
                }
            }
        
            // Restore inertiaConstraint anchor
            if (jsonObject.HasKey("inertiaConstraint"))
            {
                var inertiaConstraint = jsonObject["inertiaConstraint"].AsObject;
                if (inertiaConstraint.HasKey("anchor") && inertiaConstraint["anchor"].HasKey("path"))
                {
                    string path = inertiaConstraint["anchor"]["path"];
                    Transform anchorTransform = FindTransformByPath(character.transform, path);
                    if (anchorTransform != null)
                    {
                        inertiaConstraint["anchor"]["instanceID"] = anchorTransform.gameObject.GetInstanceID();
                    }
                }
            }
        
            // Restore distanceCullingReferenceObject
            if (jsonObject.HasKey("cullingSettings"))
            {
                var cullingSettings = jsonObject["cullingSettings"].AsObject;
                if (cullingSettings.HasKey("distanceCullingReferenceObject") &&
                    cullingSettings["distanceCullingReferenceObject"].HasKey("path"))
                {
                    string path = cullingSettings["distanceCullingReferenceObject"]["path"];
                    Transform refTransform = FindTransformByPath(character.transform, path);
                    if (refTransform != null)
                    {
                        cullingSettings["distanceCullingReferenceObject"]["instanceID"] = refTransform.gameObject.GetInstanceID();
                    }
                }
            }
        }
            
        private void InitializeSittingDetection()
        {
            try
            {
                _thighBonesByPelvis = new Dictionary<string, Dictionary<string, Transform>>();

                _rootTransform = transform.Find("Root");
                if (_rootTransform == null)
                {
                    Debug.Log(ZString.Format("Char {0} has no pelvis", gameObject.name));
                    _enableSittingDetection = false;
                    return;
                }

                FindThighBonesForSittingDetection();

                if (_thighBonesByPelvis.Count == 0)
                {
                    Debug.Log(ZString.Format(" {0} has no thigh bones", gameObject.name));
                    _enableSittingDetection = false;
                    return;
                }

                _sittingDetectionInitialized = true;
                int totalThighBones = _thighBonesByPelvis.Values.Sum(dict => dict.Count);
                Debug.Log(ZString.Format("Sitting detection initialized. Found {0} thigh bones", totalThighBones));
            }
            catch (Exception ex)
            {
                Debug.LogError(ZString.Format("Sitting down func error: {0}", ex.Message), this);
                _enableSittingDetection = false;
            }
        }
        
        private void FindThighBonesForSittingDetection()
        {
            var allTransforms = _rootTransform.GetComponentsInChildren<Transform>();

            foreach (var transform in allTransforms)
            {
                if (Regex.IsMatch(transform.name, ThighBonePattern))
                {
                    string pelvisName = ExtractPelvisNameFromThigh(transform);
                    if (string.IsNullOrEmpty(pelvisName))
                    {
                        Debug.Log(ZString.Format("Can't extract pelvis name from {0}", transform.name));
                        continue;
                    }

                    string side = transform.name.Contains("_L_") ? "L" : "R";

                    if (!_thighBonesByPelvis.ContainsKey(pelvisName))
                    {
                        _thighBonesByPelvis[pelvisName] = new Dictionary<string, Transform>();
                    }

                    _thighBonesByPelvis[pelvisName][side] = transform;
                }
            }
        }
        
        private void UpdateSittingDetection()
        {
            bool currentSittingState = CheckIfSitting();

            if (currentSittingState != _isSitting)
            {
                if (currentSittingState)
                {
                    SaveCurrentClothState();
                    ApplySittingClothState();
                }
                else
                {
                    RestoreSavedClothState();
                }

                _isSitting = currentSittingState;
                Debug.Log(ZString.Format("_isSitting : {0}", (_isSitting ? "Sit" : "Stand")));
            }
        }
        
        private bool CheckIfSitting()
        {
            if (_thighBonesByPelvis == null)
            {
                return false;
            }

            int sittingThighCount = 0;
            int totalThighCount = 0;

            foreach (var pelvisKvp in _thighBonesByPelvis)
            {
                foreach (var thighKvp in pelvisKvp.Value)
                {
                    Transform thighBone = thighKvp.Value;
                    if (thighBone == null) continue;

                    totalThighCount++;

                    bool isSittingDetected = false;

                    if (_useChildBoneDetection)
                    {
                        isSittingDetected = CheckThighToKneeDirection(thighBone);
                    }
                    else
                    {
                        isSittingDetected = CheckAbsoluteRotation(thighBone);
                    }

                    if (isSittingDetected)
                    {
                        sittingThighCount++;
                    }
                }
            }

            return sittingThighCount >= _minThighBonesForSitting;
        }

        private bool CheckThighToKneeDirection(Transform thighBone)
        {
            Transform kneeBone = FindKneeBone(thighBone);

            if (kneeBone == null)
            {
                Debug.LogWarning(ZString.Format("No knee bone found for {0}, falling back to rotation check", thighBone.name));
                return CheckAbsoluteRotation(thighBone);
            }

            Vector3 thighToKneeDirection = (kneeBone.position - thighBone.position).normalized;

            float angleFromDown = Vector3.Angle(thighToKneeDirection, Vector3.down);
            bool isSitting = angleFromDown > _sittingAngleThreshold;

            Debug.Log(ZString.Format("{0} -> {1}: Angle from down={2}° (threshold={3}°) - {4}",
                thighBone.name, kneeBone.name,
                angleFromDown.ToString("F1"),
                _sittingAngleThreshold,
                isSitting ? "Sitting" : "Standing"));

            return isSitting;
        }

        private Transform FindKneeBone(Transform thighBone)
        {
            foreach (Transform child in thighBone)
            {
                if (child.name.ToLower().Contains("calf") ||
                    child.name.ToLower().Contains("knee") ||
                    child.name.Contains("_Calf"))
                {
                    return child;
                }
            }

            if (thighBone.childCount > 0)
            {
                return thighBone.GetChild(0);
            }

            return null;
        }

        private bool CheckAbsoluteRotation(Transform thighBone)
        {
            Vector3 worldEulerAngles = thighBone.rotation.eulerAngles;

            float normalizedX = NormalizeAngle(worldEulerAngles.x);
            float normalizedY = NormalizeAngle(worldEulerAngles.y);
            float normalizedZ = NormalizeAngle(worldEulerAngles.z);

            bool isXAxisRotated = Mathf.Abs(normalizedX) > 60.0f;
            bool isYAxisRotated = Mathf.Abs(normalizedY) > 60.0f;
            bool isZAxisRotated = Mathf.Abs(normalizedZ) > 60.0f;

            if (isXAxisRotated || isYAxisRotated || isZAxisRotated)
            {
                // Debug.Log(ZString.Format("{0}: X={1}({2}), Y={3}({4}), Z={5}({6}) - Sitting detected (Absolute)",
                //     thighBone.name,
                //     normalizedX.ToString("F1"), isXAxisRotated ? "✓" : "✗",
                //     normalizedY.ToString("F1"), isYAxisRotated ? "✓" : "✗",
                //     normalizedZ.ToString("F1"), isZAxisRotated ? "✓" : "✗"));
                return true;
            }

            return false;
        }

        private static string ExtractPelvisNameFromThigh(Transform thighBone)
        {
            Transform parent = thighBone.parent;
            while (parent != null)
            {
                if (parent.name.StartsWith("Bip") && parent.name.EndsWith("_Pelvis"))
                {
                    return parent.name;
                }
                parent = parent.parent;
            }

            string thighName = thighBone.name;
            if (thighName.StartsWith("Bip") && thighName.Contains("_Thigh"))
            {
                string[] parts = thighName.Split('_');
                if (parts.Length >= 3)
                {
                    return ZString.Concat(parts[0], "_Pelvis");
                }
            }

            return null;
        }

        private void SaveCurrentClothState()
        {
            try
            {
                if (_clothComponents == null || _clothComponents.Count == 0)
                {
                    Debug.LogWarning("Can't find any cloth components");
                    return;
                }

                _savedClothState = GetCurrentClothConfigs();
                _hasBackupState = _savedClothState != null && _savedClothState.Count > 0;

                if (_hasBackupState)
                {
                    // Debug.Log(ZString.Format("Save cloth state: {0} cloth components", _savedClothState.Count));
                }
                else
                {
                    Debug.LogWarning("Save cloth state failed");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ZString.Format("Save cloth state error: {0}", ex.Message), this);
                _hasBackupState = false;
                _savedClothState = null;
            }
        }

        private void ApplySittingClothState()
        {
            try
            {
                if (_clothComponents == null || _clothComponents.Count == 0)
                    return;

                foreach (var cloth in _clothComponents)
                {
                    if (cloth != null)
                    {
                        var sdata = cloth.SerializeData;
                        if (sdata != null)
                        {
                            sdata.blendWeight = 0f;
                        }

                        cloth.enabled = false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ZString.Format("Apply sitting cloth state error: {0}", ex.Message), this);
            }
        }

        private void RestoreSavedClothState()
        {
            try
            {
                if (!_hasBackupState || _savedClothState == null)
                {
                    Debug.LogWarning("Can't restore cloth state: no backup state");
                    return;
                }

                if (_clothComponents == null || _clothComponents.Count == 0)
                {
                    Debug.LogWarning("Can't restore cloth state: no cloth components");
                    return;
                }

                foreach (var savedData in _savedClothState)
                {
                    if (savedData == null || string.IsNullOrEmpty(savedData.clothName))
                        continue;

                    var cloth = GetClothComponent(savedData.clothName);
                    if (cloth == null)
                        continue;

                    var sdata = cloth.SerializeData;
                    if (sdata != null)
                    {
                        sdata.blendWeight = Mathf.Clamp01(savedData.blendWeight);

                        cloth.enabled = savedData.enable;

                        if (savedData.cdata != null)
                        {
                            JsonUtility.FromJsonOverwrite(JsonUtility.ToJson(savedData.cdata), sdata);
                        }

                        cloth.SetParameterChange();
                    }
                }

                Debug.Log(ZString.Format("Restore cloth state: {0} cloth components", _savedClothState.Count));

                ClearSavedClothState();
            }
            catch (Exception ex)
            {
                Debug.LogError(ZString.Format("Restore cloth state error: {0}", ex.Message), this);
                ClearSavedClothState();
            }
        }

        private void ClearSavedClothState()
        {
            _savedClothState = null;
            _hasBackupState = false;
        }

        private float NormalizeAngle(float angle)
        {
            while (angle > 180f)
                angle -= 360f;
            while (angle < -180f)
                angle += 360f;
            return angle;
        }
        
        public static Transform FindTransformByPath(Transform root, string path)
        {
            if (root == null || string.IsNullOrEmpty(path))
            {
                return null;
            }
            return root.Find(path);
        }

        /// <summary>
        /// Init scene collider system
        /// </summary>
        private void ResetDynamicColliderManagement()
        {
            try
            {
                _sceneColliders.Clear();
                _colliderActiveStates.Clear();

                CollectSceneColliders();

                _dynamicColliderInitialized = true;
                _lastDistanceCheckTime = Time.time;

                Debug.Log(ZString.Format("Dynamic collider management initialized. Found {0} scene colliders.", _sceneColliders.Count));
            }
            catch (Exception ex)
            {
                Debug.LogError(ZString.Format("Dynamic collider management initialization error: {0}", ex.Message), this);
                _enableDynamicColliderManagement = false;
            }
        }

        private void CollectSceneColliders()
        {
            var allColliders = FindObjectsOfType<ColliderComponent>();

            foreach (var collider in allColliders)
            {
                if (collider == null) continue;

                if (IsCharacterGameObject(collider.transform))
                {
                    continue;
                }

                _sceneColliders.Add(collider);
                _colliderActiveStates[collider] = false;
            }
        }

        private bool IsCharacterGameObject(Transform transform)
        {
            string path = transform.GetHierarchyPath();
            return path.Contains("/Root/");
        }

        private void UpdateDynamicColliderManagement()
        {
            if (Time.time - _lastDistanceCheckTime < _distanceCheckInterval)
                return;

            _lastDistanceCheckTime = Time.time;
            ResetDynamicColliderManagement();
            try
            {
                Vector3 characterPosition = transform.position;

                foreach (var collider in _sceneColliders)
                {
                    if (collider == null) continue;

                    float distance = Vector3.Distance(characterPosition, collider.transform.position);
                    bool shouldBeActive = distance <= _colliderDistanceThreshold;

                    if (_colliderActiveStates.TryGetValue(collider, out bool currentState))
                    {
                        if (currentState != shouldBeActive)
                        {
                            UpdateColliderInClothComponents(collider, shouldBeActive);
                            _colliderActiveStates[collider] = shouldBeActive;
                        }
                    }
                    else
                    {
                        if (shouldBeActive)
                        {
                            UpdateColliderInClothComponents(collider, true);
                        }
                        _colliderActiveStates[collider] = shouldBeActive;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ZString.Format("Dynamic collider management update error: {0}", ex.Message), this);
            }
        }

        private void UpdateColliderInClothComponents(ColliderComponent collider, bool shouldAdd)
        {
            if (_clothComponents == null || collider == null)
                return;

            foreach (var cloth in _clothComponents)
            {
                if (cloth == null)
                    continue;

                var sdata = cloth.SerializeData;
                if (sdata?.colliderCollisionConstraint?.colliderList == null)
                    continue;

                var colliderList = sdata.colliderCollisionConstraint.colliderList;
                bool containsCollider = colliderList.Contains(collider);

                if (shouldAdd && !containsCollider)
                {
                    colliderList.Add(collider);
                    cloth.SetParameterChange();
                    // Debug.Log(ZString.Format("Added collider {0} to cloth {1}", collider.name, cloth.name));
                }
                else if (!shouldAdd && containsCollider)
                {
                    colliderList.Remove(collider);
                    cloth.SetParameterChange();
                    // Debug.Log(ZString.Format("Removed collider {0} from cloth {1}", collider.name, cloth.name));
                }
            }
        }

        public void RefreshSceneColliders() // public func debug
        {
            if (!_enableDynamicColliderManagement)
                return;

            _sceneColliders.Clear();
            _colliderActiveStates.Clear();
            CollectSceneColliders();

            Debug.Log(ZString.Format("Scene colliders refreshed. Found {0} colliders.", _sceneColliders.Count));
        }

        public int GetActiveCollidersCount()
        {
            if (!_enableDynamicColliderManagement)
                return 0;

            int count = 0;
            foreach (var kvp in _colliderActiveStates)
            {
                if (kvp.Value) count++;
            }
            return count;
        }

        void OnDestroy()
        {
            ClearSavedClothState();
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

