using System.Collections;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Animations;
using Cysharp.Text;
using System;

namespace CEditor
{
    [ExecuteAlways]
    public struct BodyPerformConfig
    {
        public PerformConfigMeta bodyPerformConfig;
        public BreathPerformConfig breathPerformConfig;
    }
    [ExecuteAlways]
    public struct BreathPerformConfig
    {
        public AnimationClip breathClip;
        public AvatarMask breathAvatarMask;
        public float breathWeight;
    }
    [ExecuteAlways]
    public struct EyebrowPerformConfig
    {
        public PerformConfigMeta eyebrowPerformConfig;
    }
    [ExecuteAlways]
    public struct EyesPerformConfig
    {
        public PerformConfigMeta eyesPerformConfig;
        //public EyesOverwriteAnimConfig eyesChangeInsertFrameConfig;
    }

    public struct EyesOverwritePerformConfig
    {
        public EyeOverwriteType overwriteType;
        public EyesOverwriteAnimConfig eyesOverwriteConfig;
    }
    [ExecuteAlways]
    public struct EyesOverwriteAnimConfig
    {
        public bool useEyesOverwriteAnim;
        public AnimationClip eyesOverwriteAnim;
        public float eyesOverwriteAnimFrameTime;
        public float eyesOverwriteAnimBlendInTime;
        public float eyesOverwriteAnimHoldTime;
        public float eyesOverwriteAnimBlendOutTime;
    }
    [ExecuteAlways]
    public struct MouthPerformConfig
    {
        public PerformConfigMeta mouthPerformConfig;
    }

    public enum EyeOverwriteType
    {
        CompletelyClose,
        HalfClose,
        Custom,
        None
    }

    [ExecuteAlways]
    [RequireComponent(typeof(Animator))]
    public class AnimTransPlayableSystem : MonoBehaviour
    {
        public AnimTransPlayableConfig config;
        //////////////////////////////////////////////////////////
        ///////////////////Playable动画结构元素///////////////////
        //////////////////////////////////////////////////////////
        public PlayableGraph playableGraph { get; set; }

        private bool useRandomBreath = true;

        private AnimationMixerPlayable transMixerPlayable;

        private AnimationMixerPlayable animAMixerPlayable;
        private AnimationMixerPlayable animBMixerPlayable;

        private AnimationMixerPlayable animASwitchPlayingAndLoopPlayable;
        private AnimationMixerPlayable animBSwitchPlayingAndLoopPlayable;

        private AnimationLayerMixerPlayable animABreathLayerMixerPlayable;
        private AnimationLayerMixerPlayable animBBreathLayerMixerPlayable;

        private AnimationClipPlayable breathPlayableA;
        private AnimationClipPlayable breathPlayableB;

        private AnimatorOverrideController animAPlayingController;
        private AnimatorOverrideController animBPlayingController;
        private AnimatorOverrideController animAStaticController;
        private AnimatorOverrideController animBStaticController;
        private AnimatorOverrideController animALoopController;
        private AnimatorOverrideController animBLoopController;

        private AnimatorControllerPlayable animAPlayingControllerPlayable;
        private AnimatorControllerPlayable animBPlayingControllerPlayable;
        private AnimatorControllerPlayable animAStaticControllerPlayable;
        private AnimatorControllerPlayable animBStaticControllerPlayable;
        private AnimatorControllerPlayable animALoopControllerPlayable;
        private AnimatorControllerPlayable animBLoopControllerPlayable;

        // hand overwrite
        private AnimationClipPlayable animARightHandLocalOverWriteClipPlayable;
        private AnimationClipPlayable animALeftHandLocalOverWriteClipPlayable;
        private AnimationClipPlayable animBRightHandLocalOverWriteClipPlayable;
        private AnimationClipPlayable animBLeftHandLocalOverWriteClipPlayable;

        private AnimationClipPlayable rightHandGlobalOverWriteClipPlayable;
        private AnimationClipPlayable leftHandGlobalOverWriteClipPlayable;

        private AnimationLayerMixerPlayable animALocalOverWriteLayerMixerPlayable;
        private AnimationLayerMixerPlayable animBLocalOverWriteLayerMixerPlayable;

        private AnimationLayerMixerPlayable globalOverWriteLayerMixerPlayable;

        // addictive
        //private AnimationClipPlayable globalAddictiveClipPlayable;
        //private AnimationLayerMixerPlayable globalAddictiveLayerMixerPlayable;

        // face
        private AnimationClipPlayable eyebrowAnimAClipPlayable;
        private AnimationClipPlayable eyesAnimAClipPlayable;
        private AnimationClipPlayable mouthAnimAClipPlayable;
        private AnimationClipPlayable eyebrowAnimBClipPlayable;
        private AnimationClipPlayable eyesAnimBClipPlayable;
        private AnimationClipPlayable mouthAnimBClipPlayable;

        private AnimationClipPlayable eyebrowAnimAStaticClipPlayable;
        private AnimationClipPlayable eyesAnimAStaticClipPlayable;
        private AnimationClipPlayable mouthAnimAStaticClipPlayable;
        private AnimationClipPlayable eyebrowAnimBStaticClipPlayable;
        private AnimationClipPlayable eyesAnimBStaticClipPlayable;
        private AnimationClipPlayable mouthAnimBStaticClipPlayable;

        private AnimationClipPlayable eyebrowAnimALoopClipPlayable;
        private AnimationClipPlayable eyesAnimALoopClipPlayable;
        private AnimationClipPlayable mouthAnimALoopClipPlayable;
        private AnimationClipPlayable eyebrowAnimBLoopClipPlayable;
        private AnimationClipPlayable eyesAnimBLoopClipPlayable;
        private AnimationClipPlayable mouthAnimBLoopClipPlayable;

        private AnimationClipPlayable eyesBlinkFrameAnimClipPlayable;
        private AnimationClipPlayable eyesCloseFrameAnimClipPlayable;

        private AnimationLayerMixerPlayable eyebrowAnimALayerMixerPlayable;
        private AnimationLayerMixerPlayable eyesAnimALayerMixerPlayable;
        private AnimationLayerMixerPlayable mouthAnimALayerMixerPlayable;
        private AnimationLayerMixerPlayable eyebrowAnimBLayerMixerPlayable;
        private AnimationLayerMixerPlayable eyesAnimBLayerMixerPlayable;
        private AnimationLayerMixerPlayable mouthAnimBLayerMixerPlayable;

        private AnimationLayerMixerPlayable eyebrowAnimASwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable eyesAnimASwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable mouthAnimASwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable eyebrowAnimBSwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable eyesAnimBSwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable mouthAnimBSwitchPlayingAndLoopPlayable;

        private AnimationClipPlayable eyebrowMaskAnimClipPlayable;
        private AnimationClipPlayable eyesMaskAnimClipPlayable;
        private AnimationClipPlayable mouthMaskAnimClipPlayable;

        private AnimationClipPlayable eyebrowBlankFaceAnimClipPlayable;
        private AnimationClipPlayable eyesBlankFaceAnimClipPlayable;
        private AnimationClipPlayable mouthBlankFaceAnimClipPlayable;

        private AnimationLayerMixerPlayable eyebrowWithMaskLayerMixerPlayable;
        private AnimationLayerMixerPlayable eyesWithMaskLayerMixerPlayable;
        private AnimationLayerMixerPlayable mouthWithMaskLayerMixerPlayable;

        private AnimationClipPlayable eyebrowBoneAnimAClipPlayable;
        private AnimationClipPlayable eyesBoneAnimAClipPlayable;
        private AnimationClipPlayable mouthBoneAnimAClipPlayable;
        private AnimationClipPlayable eyebrowBoneAnimBClipPlayable;
        private AnimationClipPlayable eyesBoneAnimBClipPlayable;
        private AnimationClipPlayable mouthBoneAnimBClipPlayable;

        private AnimationClipPlayable eyebrowBoneAnimAStaticClipPlayable;
        private AnimationClipPlayable eyesBoneAnimAStaticClipPlayable;
        private AnimationClipPlayable mouthBoneAnimAStaticClipPlayable;
        private AnimationClipPlayable eyebrowBoneAnimBStaticClipPlayable;
        private AnimationClipPlayable eyesBoneAnimBStaticClipPlayable;
        private AnimationClipPlayable mouthBoneAnimBStaticClipPlayable;

        private AnimationClipPlayable eyebrowBoneAnimALoopClipPlayable;
        private AnimationClipPlayable eyesBoneAnimALoopClipPlayable;
        private AnimationClipPlayable mouthBoneAnimALoopClipPlayable;
        private AnimationClipPlayable eyebrowBoneAnimBLoopClipPlayable;
        private AnimationClipPlayable eyesBoneAnimBLoopClipPlayable;
        private AnimationClipPlayable mouthBoneAnimBLoopClipPlayable;

        private AnimationLayerMixerPlayable eyebrowBoneAnimALayerMixerPlayable;
        private AnimationLayerMixerPlayable eyesBoneAnimALayerMixerPlayable;
        private AnimationLayerMixerPlayable mouthBoneAnimALayerMixerPlayable;
        private AnimationLayerMixerPlayable eyebrowBoneAnimBLayerMixerPlayable;
        private AnimationLayerMixerPlayable eyesBoneAnimBLayerMixerPlayable;
        private AnimationLayerMixerPlayable mouthBoneAnimBLayerMixerPlayable;

        private AnimationLayerMixerPlayable eyebrowBoneAnimASwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable eyesBoneAnimASwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable mouthBoneAnimASwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable eyebrowBoneAnimBSwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable eyesBoneAnimBSwitchPlayingAndLoopPlayable;
        private AnimationLayerMixerPlayable mouthBoneAnimBSwitchPlayingAndLoopPlayable;

        private AnimationLayerMixerPlayable eyebrowBoneWithMaskLayerMixerPlayable;
        private AnimationLayerMixerPlayable eyesBoneWithMaskLayerMixerPlayable;
        private AnimationLayerMixerPlayable mouthBoneWithMaskLayerMixerPlayable;

        private AnimationClipPlayable faceBlankAnimClipPlayable;
        private AnimationClipPlayable faceBSLayerMaskClipPlayable;
        private AnimationClipPlayable faceBoneLayerMaskClipPlayable;

        private AnimationLayerMixerPlayable faceBSLayerMixerPlayable;
        private AnimationLayerMixerPlayable faceBoneLayerMixerPlayable;

        private AnimationLayerMixerPlayable faceBaseLayerMixerPlayable;

        //private AnimationClipPlayable faceAddictiveBlankClipPlayable;
        //private AnimationClipPlayable faceAddictiveClipPlayable;
        //private AnimationLayerMixerPlayable faceAddictiveMixerPlayable;
        //private AnimationLayerMixerPlayable faceFinalLayerMixerPlayable;

        private AnimationLayerMixerPlayable bodyWithFaceLayerMixerPlayable;

        // pose Catch
        private AnimationClipPlayable poseCatchAnimPlayable;
        private AnimationLayerMixerPlayable poseCatchLayerMixerPlayable;

        // debug
        private AnimationClipPlayable debugMaskBaseClipPlayable;
        private AnimationLayerMixerPlayable debugMaskLayerMixerPlayable;


        //////////////////////////////////////////////////////////
        /////////////////////body资产参数设置/////////////////////
        //////////////////////////////////////////////////////////
        private AnimTransUnit bodyAnimTransUnit;

        // animator override controller
        //public RuntimeAnimatorController playingOverrideControllerTemplate;
        //public RuntimeAnimatorController staticOverrideControllerTemplate;

        // 呼吸
        private BreathPerformConfig animABreathPerformConfig;
        private BreathPerformConfig animBBreathPerformConfig;

        // 双手重写
        //public AvatarMask rightHandMask;
        //public AvatarMask leftHandMask;

        private AnimationClip localAnimARightHandClip;
        private AnimationClip localAnimALeftHandClip;
        private AnimationClip localAnimBRightHandClip;
        private AnimationClip localAnimBLeftHandClip;
        private AnimationClip globalRightHandClip;
        private AnimationClip globalLeftHandClip;

        // addictive
        //private AnimationClip globalAddictiveClip;
        //private AvatarMask globalAddictiveMask;
        //private float globalAddictiveWeight = 0f;

        private bool debugMode = false;
        private bool showDebugStep = false;
        private int fullDebugStep = 10;
        private int debugStep = 0;
        //public AvatarMask debugMask;
        //public AnimationClip debugIdleClip;


        //////////////////////////////////////////////////////////
        /////////////////////face资产参数设置/////////////////////
        //////////////////////////////////////////////////////////
        private AnimTransUnit eyebrowAnimTransUnit;
        private AnimTransUnit eyesAnimTransUnit;
        private AnimTransUnit mouthAnimTransUnit;

        //public AnimationClip eyebrowMaskAnimClip;
        //public AnimationClip eyesMaskAnimClip;
        //public AnimationClip mouthMaskAnimClip;
        //public AnimationClip blankFaceAnimClip;

        //public AvatarMask eyebrowBoneMask;
        //public AvatarMask eyesBoneMask;
        //public AvatarMask mouthBoneMask;

        //public AnimationClip faceBSLayerMask;
        //public AnimationClip faceBoneLayerMask;
        //public AnimationClip faceBSBoneBlankClip;

        //private AnimationClip faceAddictiveClip;

        //////////////////////////////////////////////////////////
        /////////////////////face资产参数设置/////////////////////
        //////////////////////////////////////////////////////////
        private AnimationClip poseCatchAnimClip;
        private float poseCatchAnimTime;

        //////////////////////////////////////////////////////////
        ////////////////////playable运行时参数////////////////////
        //////////////////////////////////////////////////////////


        //////////////////////////////////////////////////////////
        //////////////////////body运行时参数//////////////////////
        //////////////////////////////////////////////////////////
        private BreathPerformConfig initBreathConfig;

        private bool rightHandOverWriteMaskIsOK = false;
        private bool leftHandOverWriteMaskIsOK = false;

        private BodyPerformConfig lastBodyConfig;
        private bool readyBodyTrans = false;

        private bool useLoopBodyTrans = false;
        private bool loopBodyTransEvaluateImmediately = false;

        //////////////////////////////////////////////////////////
        //////////////////////face运行时参数//////////////////////
        //////////////////////////////////////////////////////////
        private EyebrowPerformConfig lastEyebrowConfig;
        private bool readyEyebrowTrans = false;

        private EyesPerformConfig lastEyesConfig;
        private bool readyEyesTrans = false;

        private MouthPerformConfig lastMouthConfig;
        private bool readyMouthTrans = false;

        private EyesOverwritePerformConfig lastBlinkConfig;
        private EyesOverwritePerformConfig lastEyesCloseConfig;

        private bool eyesBlinkInFinish = false;
        private bool eyesBlinkOutFinish = false;
        private float eyesBlinkDurationTime = 0;
        private float eyesBlinkAnimLayerWeight = 0;

        private bool eyesCloseInFinish = false;
        private bool eyesCloseOutFinish = false;
        private float eyesCloseDurationTime = 0;
        private float eyesCloseAnimLayerWeight = 0;
        private bool eyesCloseHoldTimeControlByEvent = false;

        private bool eyesTransExecuteOnce = false;

        //////////////////////////////////////////////////////////
        //////////////////////宏观运行时参数//////////////////////
        //////////////////////////////////////////////////////////
        private bool bodyJustInit;
        private bool eyebrowJustInit;
        private bool eyesJustInit;
        private bool mouthJustInit;


        // Start is called before the first frame update
        void Start()
        {
            
        }

        // Update is called once per frame
        void Update()
        {
            UpdateSyetem();
        }

        void OnDisable()
        {
            //销毁该图创建的所有可播放项和 PlayableOutput。
            if (playableGraph.IsValid())
                playableGraph.Destroy();
        }

        public bool InitAndStartPlayableSystem(BodyPerformConfig bodyConfig, EyebrowPerformConfig eyebrowConfig, EyesPerformConfig eyesConfig, MouthPerformConfig mouthConfig)
        {
            Debug.Log("animTransPlayableSystem InitAndStartPlayableSystem");
            if (!AnimTransSystemIsWorking())
            {
                bodyAnimTransUnit = new AnimTransUnit();
                eyebrowAnimTransUnit = new AnimTransUnit();
                eyesAnimTransUnit = new AnimTransUnit();
                mouthAnimTransUnit = new AnimTransUnit();

                if (config != null)
                {
                    bodyConfig.bodyPerformConfig.animationClip = config.defaultStandIdleClip;
                    bodyConfig.breathPerformConfig.breathClip = config.defaultStandIdleClip;
                    eyebrowConfig.eyebrowPerformConfig.animationClip = config.faceBSBoneBlankClip;
                    eyesConfig.eyesPerformConfig.animationClip = config.faceBSBoneBlankClip;
                    mouthConfig.mouthPerformConfig.animationClip = config.faceBSBoneBlankClip;
                    lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.blankFaceAnimClip;

                    //if (bodyConfig.bodyPerformConfig.animationClip == null)
                    //    bodyConfig.bodyPerformConfig.animationClip = config.defaultStandIdleClip;
                    //if (bodyConfig.breathPerformConfig.breathClip == null)
                    //    bodyConfig.breathPerformConfig.breathClip = config.defaultStandIdleClip;
                    //if (eyebrowConfig.eyebrowPerformConfig.animationClip == null)
                    //    eyebrowConfig.eyebrowPerformConfig.animationClip = config.faceBSBoneBlankClip;
                    //if (eyesConfig.eyesPerformConfig.animationClip == null)
                    //    eyesConfig.eyesPerformConfig.animationClip = config.faceBSBoneBlankClip;
                    //if (eyesConfig.eyesChangeInsertFrameConfig.eyesOverwriteAnim == null)
                    //    eyesConfig.eyesChangeInsertFrameConfig.eyesOverwriteAnim = config.blankFaceAnimClip;
                    //if (mouthConfig.mouthPerformConfig.animationClip == null)
                    //    mouthConfig.mouthPerformConfig.animationClip = config.faceBSBoneBlankClip;


                    bodyAnimTransUnit.InitTransUnit(bodyConfig.bodyPerformConfig);
                    initBreathConfig = bodyConfig.breathPerformConfig;

                    eyebrowAnimTransUnit.InitTransUnit(eyebrowConfig.eyebrowPerformConfig);
                    eyesAnimTransUnit.InitTransUnit(eyesConfig.eyesPerformConfig);
                    mouthAnimTransUnit.InitTransUnit(mouthConfig.mouthPerformConfig);

                    CreateGraph();
                    InitAnimSystem();
                    SetAnimStateToJustInit();

                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;
        }

        public void SetAnimStateToJustInit()
        {
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  SetAnimStateToJustInit"));
            bodyJustInit = true;
            eyebrowJustInit = true;
            eyesJustInit = true;
            mouthJustInit = true;
        }

        private void UpdateSyetem()
        {
            if (AnimTransSystemIsWorking())
            {
                //if (readyBodyTrans)
                //{
                //    //ExecuteBodyTrans(lastBodyConfig);
                //    readyBodyTrans = false;
                //}
                //if (readyEyebrowTrans)
                //{
                //    //ExecuteEyebrowTrans(lastEyebrowConfig);
                //    readyEyebrowTrans = false;
                //}

                if (useLoopBodyTrans)
                    LoopBodyTrans(loopBodyTransEvaluateImmediately);


                UpdateEyesBlinkState();
                UpdateEyesCloseState();

                if (readyEyesTrans)
                {
                    if (eyesBlinkInFinish)
                    {
                        if ((!eyesBlinkOutFinish) && (!eyesTransExecuteOnce))
                        {
                            ExecuteEyesTrans(lastEyesConfig);
                            eyesTransExecuteOnce = true;
                        }
                        if (eyesBlinkOutFinish)
                        {
                            readyEyesTrans = false;
                        }
                    }
                    if (eyesBlinkInFinish && eyesBlinkOutFinish)
                        readyEyesTrans = false;
                    //else
                    //{
                    //    ExecuteEyesTrans(lastEyesConfig);
                    //    readyEyesTrans = false;
                    //}
                }

                //if (readyMouthTrans)
                //{
                //    //ExecuteMouthTrans(lastMouthConfig);
                //    readyMouthTrans = false;
                //}
                if (bodyAnimTransUnit != null)
                    bodyAnimTransUnit.UpdateTransUnit();
                if (eyebrowAnimTransUnit != null)
                    eyebrowAnimTransUnit.UpdateTransUnit();
                if (eyesAnimTransUnit != null)
                    eyesAnimTransUnit.UpdateTransUnit();
                if (mouthAnimTransUnit != null)
                    mouthAnimTransUnit.UpdateTransUnit();

                UpdateGraphBlendWeight(debugMode);
                //DebugLog();
            }
        }

        private void CreateGraph()
        {
            if (config == null)
                return;

            playableGraph = PlayableGraph.Create(name);
            playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);

            var playableOutput = AnimationPlayableOutput.Create(playableGraph, "Animation", GetComponent<Animator>());

            animAMixerPlayable = AnimationMixerPlayable.Create(playableGraph, 2);
            animBMixerPlayable = AnimationMixerPlayable.Create(playableGraph, 2);

            animASwitchPlayingAndLoopPlayable = AnimationMixerPlayable.Create(playableGraph, 2);
            animBSwitchPlayingAndLoopPlayable = AnimationMixerPlayable.Create(playableGraph, 2);

            transMixerPlayable = AnimationMixerPlayable.Create(playableGraph, 2);

            // 用animator controller包裹的clip，用于实现mirror和speed的控制
            animAPlayingController = new AnimatorOverrideController(config.playingOverrideControllerTemplate);
            animAPlayingControllerPlayable = AnimatorControllerPlayable.Create(playableGraph, animAPlayingController);
            animBPlayingController = new AnimatorOverrideController(config.playingOverrideControllerTemplate);
            animBPlayingControllerPlayable = AnimatorControllerPlayable.Create(playableGraph, animBPlayingController);
            animAStaticController = new AnimatorOverrideController(config.staticOverrideControllerTemplate);
            animAStaticControllerPlayable = AnimatorControllerPlayable.Create(playableGraph, animAStaticController);
            animBStaticController = new AnimatorOverrideController(config.staticOverrideControllerTemplate);
            animBStaticControllerPlayable = AnimatorControllerPlayable.Create(playableGraph, animBStaticController);
            animALoopController = new AnimatorOverrideController(config.playingOverrideControllerTemplate);
            animALoopControllerPlayable = AnimatorControllerPlayable.Create(playableGraph, animALoopController);
            animBLoopController = new AnimatorOverrideController(config.playingOverrideControllerTemplate);
            animBLoopControllerPlayable = AnimatorControllerPlayable.Create(playableGraph, animBLoopController);

            // breath
            breathPlayableA = AnimationClipPlayable.Create(playableGraph, animABreathPerformConfig.breathClip);
            breathPlayableB = AnimationClipPlayable.Create(playableGraph, animBBreathPerformConfig.breathClip);

            animABreathLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            animABreathLayerMixerPlayable.SetLayerAdditive(1, true);
            if (animABreathPerformConfig.breathAvatarMask != null)
                animABreathLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, animABreathPerformConfig.breathAvatarMask);
            animABreathLayerMixerPlayable.SetInputWeight(0, 1);

            animBBreathLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            animBBreathLayerMixerPlayable.SetLayerAdditive(1, true);
            if (animBBreathPerformConfig.breathAvatarMask != null)
                animBBreathLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, animBBreathPerformConfig.breathAvatarMask);
            animBBreathLayerMixerPlayable.SetInputWeight(0, 1);

            // hand overwrite
            animARightHandLocalOverWriteClipPlayable = AnimationClipPlayable.Create(playableGraph, localAnimARightHandClip);
            animALeftHandLocalOverWriteClipPlayable = AnimationClipPlayable.Create(playableGraph, localAnimALeftHandClip);
            animBRightHandLocalOverWriteClipPlayable = AnimationClipPlayable.Create(playableGraph, localAnimBRightHandClip);
            animBLeftHandLocalOverWriteClipPlayable = AnimationClipPlayable.Create(playableGraph, localAnimBLeftHandClip);
            rightHandGlobalOverWriteClipPlayable = AnimationClipPlayable.Create(playableGraph, globalRightHandClip);
            leftHandGlobalOverWriteClipPlayable = AnimationClipPlayable.Create(playableGraph, globalLeftHandClip);

            animALocalOverWriteLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 3);
            animBLocalOverWriteLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 3);
            globalOverWriteLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 3);

            animALocalOverWriteLayerMixerPlayable.SetInputWeight(0, 1);
            animALocalOverWriteLayerMixerPlayable.SetInputWeight(1, 0);
            animALocalOverWriteLayerMixerPlayable.SetInputWeight(2, 0);
            animBLocalOverWriteLayerMixerPlayable.SetInputWeight(0, 1);
            animBLocalOverWriteLayerMixerPlayable.SetInputWeight(1, 0);
            animBLocalOverWriteLayerMixerPlayable.SetInputWeight(2, 0);
            globalOverWriteLayerMixerPlayable.SetInputWeight(0, 1);
            globalOverWriteLayerMixerPlayable.SetInputWeight(1, 0);
            globalOverWriteLayerMixerPlayable.SetInputWeight(2, 0);

            if (config.rightHandMask != null)
            {
                animALocalOverWriteLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, config.rightHandMask);
                animBLocalOverWriteLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, config.rightHandMask);
                globalOverWriteLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, config.rightHandMask);
                rightHandOverWriteMaskIsOK = true;
            }
            else rightHandOverWriteMaskIsOK = false;

            if (config.leftHandMask != null)
            {
                animALocalOverWriteLayerMixerPlayable.SetLayerMaskFromAvatarMask(2, config.leftHandMask);
                animBLocalOverWriteLayerMixerPlayable.SetLayerMaskFromAvatarMask(2, config.leftHandMask);
                globalOverWriteLayerMixerPlayable.SetLayerMaskFromAvatarMask(2, config.leftHandMask);
                leftHandOverWriteMaskIsOK = true;
            }
            else leftHandOverWriteMaskIsOK = false;

            // addictive
            //globalAddictiveClipPlayable = AnimationClipPlayable.Create(playableGraph, globalAddictiveClip);
            //globalAddictiveLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            //if (globalAddictiveMask != null)
            //    globalAddictiveLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, globalAddictiveMask);
            //globalAddictiveLayerMixerPlayable.SetLayerAdditive(1, true);
            //globalAddictiveLayerMixerPlayable.SetInputWeight(0, 1);
            //globalAddictiveLayerMixerPlayable.SetInputWeight(1, Mathf.Clamp01(globalAddictiveWeight));

            // debug
            debugMaskLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            if (config != null && config.debugMask != null)
                debugMaskLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, config.debugMask);
            debugMaskLayerMixerPlayable.SetInputWeight(0, 1);
            debugMaskBaseClipPlayable = AnimationClipPlayable.Create(playableGraph, config.defaultStandIdleClip);
            debugMaskBaseClipPlayable.SetTime(0);
            debugMaskBaseClipPlayable.Pause();

            // pose catch
            poseCatchLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            poseCatchLayerMixerPlayable.SetInputWeight(0, 1);
            poseCatchLayerMixerPlayable.SetInputWeight(1, 0);
            if (poseCatchAnimClip != null)
                poseCatchAnimPlayable = AnimationClipPlayable.Create(playableGraph, poseCatchAnimClip);
            else if (config != null && config.defaultStandIdleClip != null)
                poseCatchAnimPlayable = AnimationClipPlayable.Create(playableGraph, config.defaultStandIdleClip);
            else
                poseCatchAnimPlayable = AnimationClipPlayable.Create(playableGraph, null);

            // face
            bodyWithFaceLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            //faceFinalLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            faceBaseLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            faceBSLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 4);
            faceBoneLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 4);

            faceBSLayerMaskClipPlayable = AnimationClipPlayable.Create(playableGraph, config.faceBSLayerMask);
            faceBoneLayerMaskClipPlayable = AnimationClipPlayable.Create(playableGraph, config.faceBoneLayerMask);
            faceBlankAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, config.faceBSBoneBlankClip);

            // face BS
            eyebrowWithMaskLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 4);
            eyesWithMaskLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 6);
            mouthWithMaskLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 4);

            eyebrowAnimALayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesAnimALayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthAnimALayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyebrowAnimBLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesAnimBLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthAnimBLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            eyebrowAnimASwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesAnimASwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthAnimASwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyebrowAnimBSwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesAnimBSwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthAnimBSwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            eyebrowAnimAClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animAClip);
            eyesAnimAClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animAClip);
            mouthAnimAClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animAClip);
            eyebrowAnimAStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animAClip);
            eyesAnimAStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animAClip);
            mouthAnimAStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animAClip);
            eyebrowAnimALoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animAClip);
            eyesAnimALoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animAClip);
            mouthAnimALoopClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animAClip);

            eyebrowAnimBClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animBClip);
            eyesAnimBClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animBClip);
            mouthAnimBClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animBClip);
            eyebrowAnimBStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animBClip);
            eyesAnimBStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animBClip);
            mouthAnimBStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animBClip);
            eyebrowAnimBLoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animBClip);
            eyesAnimBLoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animBClip);
            mouthAnimBLoopClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animBClip);

            eyesBlinkFrameAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim);
            eyesCloseFrameAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim);

            eyebrowMaskAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, config.eyebrowMaskAnimClip);
            eyesMaskAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, config.eyesMaskAnimClip);
            mouthMaskAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, config.mouthMaskAnimClip);

            eyebrowBlankFaceAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, config.blankFaceAnimClip);
            eyesBlankFaceAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, config.blankFaceAnimClip);
            mouthBlankFaceAnimClipPlayable = AnimationClipPlayable.Create(playableGraph, config.blankFaceAnimClip);

            // face Bone
            eyebrowBoneWithMaskLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesBoneWithMaskLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthBoneWithMaskLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            eyebrowBoneAnimALayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesBoneAnimALayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthBoneAnimALayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyebrowBoneAnimBLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesBoneAnimBLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthBoneAnimBLayerMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            eyebrowBoneAnimASwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesBoneAnimASwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthBoneAnimASwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyebrowBoneAnimBSwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            eyesBoneAnimBSwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            mouthBoneAnimBSwitchPlayingAndLoopPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);

            eyebrowBoneAnimAClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animAClip);
            eyesBoneAnimAClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animAClip);
            mouthBoneAnimAClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animAClip);
            eyebrowBoneAnimAStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animAClip);
            eyesBoneAnimAStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animAClip);
            mouthBoneAnimAStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animAClip);
            eyebrowBoneAnimALoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animAClip);
            eyesBoneAnimALoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animAClip);
            mouthBoneAnimALoopClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animAClip);

            eyebrowBoneAnimBClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animBClip);
            eyesBoneAnimBClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animBClip);
            mouthBoneAnimBClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animBClip);
            eyebrowBoneAnimBStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animBClip);
            eyesBoneAnimBStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animBClip);
            mouthBoneAnimBStaticClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animBClip);
            eyebrowBoneAnimBLoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyebrowAnimTransUnit.animBClip);
            eyesBoneAnimBLoopClipPlayable = AnimationClipPlayable.Create(playableGraph, eyesAnimTransUnit.animBClip);
            mouthBoneAnimBLoopClipPlayable = AnimationClipPlayable.Create(playableGraph, mouthAnimTransUnit.animBClip);

            //faceAddictiveMixerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 2);
            //faceAddictiveBlankClipPlayable = AnimationClipPlayable.Create(playableGraph, null);
            //faceAddictiveClipPlayable = AnimationClipPlayable.Create(playableGraph, faceAddictiveClip);

            bodyWithFaceLayerMixerPlayable.SetInputWeight(0, 1);
            bodyWithFaceLayerMixerPlayable.SetInputWeight(1, 1);
            if (config.faceBodyPartitionMask != null)
                bodyWithFaceLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, config.faceBodyPartitionMask);

            //faceFinalLayerMixerPlayable.SetInputWeight(0, 1);
            //faceFinalLayerMixerPlayable.SetInputWeight(1, 1);
            //faceFinalLayerMixerPlayable.SetLayerAdditive(1, true);

            faceBaseLayerMixerPlayable.SetInputWeight(0, 1);
            faceBaseLayerMixerPlayable.SetInputWeight(1, 1);
            faceBaseLayerMixerPlayable.SetLayerAdditive(1, true);

            eyebrowAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyesAnimALayerMixerPlayable.SetInputWeight(0, 1);
            mouthAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyesAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            mouthAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowAnimALayerMixerPlayable.SetInputWeight(1, 0);
            eyesAnimALayerMixerPlayable.SetInputWeight(1, 0);
            mouthAnimALayerMixerPlayable.SetInputWeight(1, 0);
            eyebrowAnimBLayerMixerPlayable.SetInputWeight(1, 0);
            eyesAnimBLayerMixerPlayable.SetInputWeight(1, 0);
            mouthAnimBLayerMixerPlayable.SetInputWeight(1, 0);

            eyebrowBoneAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyesBoneAnimALayerMixerPlayable.SetInputWeight(0, 1);
            mouthBoneAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowBoneAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyesBoneAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            mouthBoneAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowBoneAnimALayerMixerPlayable.SetInputWeight(1, 0);
            eyesBoneAnimALayerMixerPlayable.SetInputWeight(1, 0);
            mouthBoneAnimALayerMixerPlayable.SetInputWeight(1, 0);
            eyebrowBoneAnimBLayerMixerPlayable.SetInputWeight(1, 0);
            eyesBoneAnimBLayerMixerPlayable.SetInputWeight(1, 0);
            mouthBoneAnimBLayerMixerPlayable.SetInputWeight(1, 0);

            faceBSLayerMixerPlayable.SetInputWeight(0, 1);
            faceBSLayerMixerPlayable.SetInputWeight(1, 1);
            faceBSLayerMixerPlayable.SetInputWeight(2, 1);
            faceBSLayerMixerPlayable.SetInputWeight(3, 1);
            faceBSLayerMixerPlayable.SetLayerAdditive(1, true);
            faceBSLayerMixerPlayable.SetLayerAdditive(2, true);

            faceBoneLayerMixerPlayable.SetInputWeight(0, 1);
            faceBoneLayerMixerPlayable.SetInputWeight(1, 1);
            faceBoneLayerMixerPlayable.SetInputWeight(2, 1);
            faceBoneLayerMixerPlayable.SetInputWeight(3, 1);
            faceBoneLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, config.eyesBoneMask);
            faceBoneLayerMixerPlayable.SetLayerMaskFromAvatarMask(2, config.mouthBoneMask);

            eyebrowWithMaskLayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowWithMaskLayerMixerPlayable.SetInputWeight(3, 1);
            eyesWithMaskLayerMixerPlayable.SetInputWeight(0, 1);
            eyesWithMaskLayerMixerPlayable.SetInputWeight(5, 1);
            mouthWithMaskLayerMixerPlayable.SetInputWeight(0, 1);
            mouthWithMaskLayerMixerPlayable.SetInputWeight(3, 1);

            //faceAddictiveMixerPlayable.SetInputWeight(0, 1);
            //faceAddictiveMixerPlayable.SetInputWeight(1, 1);

            // Playable结构
            playableGraph.Connect(animAStaticControllerPlayable, 0, animABreathLayerMixerPlayable, 0);
            playableGraph.Connect(breathPlayableA, 0, animABreathLayerMixerPlayable, 1);

            playableGraph.Connect(animBStaticControllerPlayable, 0, animBBreathLayerMixerPlayable, 0);
            playableGraph.Connect(breathPlayableB, 0, animBBreathLayerMixerPlayable, 1);

            playableGraph.Connect(animAPlayingControllerPlayable, 0, animAMixerPlayable, 0);
            playableGraph.Connect(animABreathLayerMixerPlayable, 0, animAMixerPlayable, 1);

            playableGraph.Connect(animBPlayingControllerPlayable, 0, animBMixerPlayable, 0);
            playableGraph.Connect(animBBreathLayerMixerPlayable, 0, animBMixerPlayable, 1);

            playableGraph.Connect(animAMixerPlayable, 0, animASwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(animALoopControllerPlayable, 0, animASwitchPlayingAndLoopPlayable, 1);

            playableGraph.Connect(animBMixerPlayable, 0, animBSwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(animBLoopControllerPlayable, 0, animBSwitchPlayingAndLoopPlayable, 1);

            playableGraph.Connect(animASwitchPlayingAndLoopPlayable, 0, animALocalOverWriteLayerMixerPlayable, 0);
            playableGraph.Connect(animARightHandLocalOverWriteClipPlayable, 0, animALocalOverWriteLayerMixerPlayable, 1);
            playableGraph.Connect(animALeftHandLocalOverWriteClipPlayable, 0, animALocalOverWriteLayerMixerPlayable, 2);

            playableGraph.Connect(animBSwitchPlayingAndLoopPlayable, 0, animBLocalOverWriteLayerMixerPlayable, 0);
            playableGraph.Connect(animBRightHandLocalOverWriteClipPlayable, 0, animBLocalOverWriteLayerMixerPlayable, 1);
            playableGraph.Connect(animBLeftHandLocalOverWriteClipPlayable, 0, animBLocalOverWriteLayerMixerPlayable, 2);

            playableGraph.Connect(animALocalOverWriteLayerMixerPlayable, 0, transMixerPlayable, 0);
            playableGraph.Connect(animBLocalOverWriteLayerMixerPlayable, 0, transMixerPlayable, 1);

            playableGraph.Connect(transMixerPlayable, 0, globalOverWriteLayerMixerPlayable, 0);
            playableGraph.Connect(rightHandGlobalOverWriteClipPlayable, 0, globalOverWriteLayerMixerPlayable, 1);
            playableGraph.Connect(leftHandGlobalOverWriteClipPlayable, 0, globalOverWriteLayerMixerPlayable, 2);

            //playableGraph.Connect(globalOverWriteLayerMixerPlayable, 0, globalAddictiveLayerMixerPlayable, 0);
            //playableGraph.Connect(globalAddictiveClipPlayable, 0, globalAddictiveLayerMixerPlayable, 1);

            playableGraph.Connect(eyebrowAnimAClipPlayable, 0, eyebrowAnimALayerMixerPlayable, 0);
            playableGraph.Connect(eyebrowAnimAStaticClipPlayable, 0, eyebrowAnimALayerMixerPlayable, 1);
            playableGraph.Connect(eyesAnimAClipPlayable, 0, eyesAnimALayerMixerPlayable, 0);
            playableGraph.Connect(eyesAnimAStaticClipPlayable, 0, eyesAnimALayerMixerPlayable, 1);
            playableGraph.Connect(mouthAnimAClipPlayable, 0, mouthAnimALayerMixerPlayable, 0);
            playableGraph.Connect(mouthAnimAStaticClipPlayable, 0, mouthAnimALayerMixerPlayable, 1);
            playableGraph.Connect(eyebrowAnimBClipPlayable, 0, eyebrowAnimBLayerMixerPlayable, 0);
            playableGraph.Connect(eyebrowAnimBStaticClipPlayable, 0, eyebrowAnimBLayerMixerPlayable, 1);
            playableGraph.Connect(eyesAnimBClipPlayable, 0, eyesAnimBLayerMixerPlayable, 0);
            playableGraph.Connect(eyesAnimBStaticClipPlayable, 0, eyesAnimBLayerMixerPlayable, 1);
            playableGraph.Connect(mouthAnimBClipPlayable, 0, mouthAnimBLayerMixerPlayable, 0);
            playableGraph.Connect(mouthAnimBStaticClipPlayable, 0, mouthAnimBLayerMixerPlayable, 1);

            playableGraph.Connect(eyebrowAnimALayerMixerPlayable, 0, eyebrowAnimASwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyebrowAnimALoopClipPlayable, 0, eyebrowAnimASwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(eyesAnimALayerMixerPlayable, 0, eyesAnimASwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyesAnimALoopClipPlayable, 0, eyesAnimASwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(mouthAnimALayerMixerPlayable, 0, mouthAnimASwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(mouthAnimALoopClipPlayable, 0, mouthAnimASwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(eyebrowAnimBLayerMixerPlayable, 0, eyebrowAnimBSwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyebrowAnimBLoopClipPlayable, 0, eyebrowAnimBSwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(eyesAnimBLayerMixerPlayable, 0, eyesAnimBSwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyesAnimBLoopClipPlayable, 0, eyesAnimBSwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(mouthAnimBLayerMixerPlayable, 0, mouthAnimBSwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(mouthAnimBLoopClipPlayable, 0, mouthAnimBSwitchPlayingAndLoopPlayable, 1);

            playableGraph.Connect(eyebrowBlankFaceAnimClipPlayable, 0, eyebrowWithMaskLayerMixerPlayable, 0);
            playableGraph.Connect(eyebrowAnimASwitchPlayingAndLoopPlayable, 0, eyebrowWithMaskLayerMixerPlayable, 1);
            playableGraph.Connect(eyebrowAnimBSwitchPlayingAndLoopPlayable, 0, eyebrowWithMaskLayerMixerPlayable, 2);
            playableGraph.Connect(eyebrowMaskAnimClipPlayable, 0, eyebrowWithMaskLayerMixerPlayable, 3);

            playableGraph.Connect(eyesBlankFaceAnimClipPlayable, 0, eyesWithMaskLayerMixerPlayable, 0);
            playableGraph.Connect(eyesAnimASwitchPlayingAndLoopPlayable, 0, eyesWithMaskLayerMixerPlayable, 1);
            playableGraph.Connect(eyesAnimBSwitchPlayingAndLoopPlayable, 0, eyesWithMaskLayerMixerPlayable, 2);
            playableGraph.Connect(eyesCloseFrameAnimClipPlayable, 0, eyesWithMaskLayerMixerPlayable, 3);
            playableGraph.Connect(eyesBlinkFrameAnimClipPlayable, 0, eyesWithMaskLayerMixerPlayable, 4);
            playableGraph.Connect(eyesMaskAnimClipPlayable, 0, eyesWithMaskLayerMixerPlayable, 5);

            playableGraph.Connect(mouthBlankFaceAnimClipPlayable, 0, mouthWithMaskLayerMixerPlayable, 0);
            playableGraph.Connect(mouthAnimASwitchPlayingAndLoopPlayable, 0, mouthWithMaskLayerMixerPlayable, 1);
            playableGraph.Connect(mouthAnimBSwitchPlayingAndLoopPlayable, 0, mouthWithMaskLayerMixerPlayable, 2);
            playableGraph.Connect(mouthMaskAnimClipPlayable, 0, mouthWithMaskLayerMixerPlayable, 3);

            playableGraph.Connect(eyebrowWithMaskLayerMixerPlayable, 0, faceBSLayerMixerPlayable, 0);
            playableGraph.Connect(eyesWithMaskLayerMixerPlayable, 0, faceBSLayerMixerPlayable, 1);
            playableGraph.Connect(mouthWithMaskLayerMixerPlayable, 0, faceBSLayerMixerPlayable, 2);
            playableGraph.Connect(faceBSLayerMaskClipPlayable, 0, faceBSLayerMixerPlayable, 3);

            playableGraph.Connect(eyebrowBoneAnimAClipPlayable, 0, eyebrowBoneAnimALayerMixerPlayable, 0);
            playableGraph.Connect(eyebrowBoneAnimAStaticClipPlayable, 0, eyebrowBoneAnimALayerMixerPlayable, 1);
            playableGraph.Connect(eyesBoneAnimAClipPlayable, 0, eyesBoneAnimALayerMixerPlayable, 0);
            playableGraph.Connect(eyesBoneAnimAStaticClipPlayable, 0, eyesBoneAnimALayerMixerPlayable, 1);
            playableGraph.Connect(mouthBoneAnimAClipPlayable, 0, mouthBoneAnimALayerMixerPlayable, 0);
            playableGraph.Connect(mouthBoneAnimAStaticClipPlayable, 0, mouthBoneAnimALayerMixerPlayable, 1);
            playableGraph.Connect(eyebrowBoneAnimBClipPlayable, 0, eyebrowBoneAnimBLayerMixerPlayable, 0);
            playableGraph.Connect(eyebrowBoneAnimBStaticClipPlayable, 0, eyebrowBoneAnimBLayerMixerPlayable, 1);
            playableGraph.Connect(eyesBoneAnimBClipPlayable, 0, eyesBoneAnimBLayerMixerPlayable, 0);
            playableGraph.Connect(eyesBoneAnimBStaticClipPlayable, 0, eyesBoneAnimBLayerMixerPlayable, 1);
            playableGraph.Connect(mouthBoneAnimBClipPlayable, 0, mouthBoneAnimBLayerMixerPlayable, 0);
            playableGraph.Connect(mouthBoneAnimBStaticClipPlayable, 0, mouthBoneAnimBLayerMixerPlayable, 1);

            playableGraph.Connect(eyebrowBoneAnimALayerMixerPlayable, 0, eyebrowBoneAnimASwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyebrowBoneAnimALoopClipPlayable, 0, eyebrowBoneAnimASwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(eyesBoneAnimALayerMixerPlayable, 0, eyesBoneAnimASwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyesBoneAnimALoopClipPlayable, 0, eyesBoneAnimASwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(mouthBoneAnimALayerMixerPlayable, 0, mouthBoneAnimASwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(mouthBoneAnimALoopClipPlayable, 0, mouthBoneAnimASwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(eyebrowBoneAnimBLayerMixerPlayable, 0, eyebrowBoneAnimBSwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyebrowBoneAnimBLoopClipPlayable, 0, eyebrowBoneAnimBSwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(eyesBoneAnimBLayerMixerPlayable, 0, eyesBoneAnimBSwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(eyesBoneAnimBLoopClipPlayable, 0, eyesBoneAnimBSwitchPlayingAndLoopPlayable, 1);
            playableGraph.Connect(mouthBoneAnimBLayerMixerPlayable, 0, mouthBoneAnimBSwitchPlayingAndLoopPlayable, 0);
            playableGraph.Connect(mouthBoneAnimBLoopClipPlayable, 0, mouthBoneAnimBSwitchPlayingAndLoopPlayable, 1);

            playableGraph.Connect(eyebrowBoneAnimASwitchPlayingAndLoopPlayable, 0, eyebrowBoneWithMaskLayerMixerPlayable, 0);
            playableGraph.Connect(eyebrowBoneAnimBSwitchPlayingAndLoopPlayable, 0, eyebrowBoneWithMaskLayerMixerPlayable, 1);

            playableGraph.Connect(eyesBoneAnimASwitchPlayingAndLoopPlayable, 0, eyesBoneWithMaskLayerMixerPlayable, 0);
            playableGraph.Connect(eyesBoneAnimBSwitchPlayingAndLoopPlayable, 0, eyesBoneWithMaskLayerMixerPlayable, 1);

            playableGraph.Connect(mouthBoneAnimASwitchPlayingAndLoopPlayable, 0, mouthBoneWithMaskLayerMixerPlayable, 0);
            playableGraph.Connect(mouthBoneAnimBSwitchPlayingAndLoopPlayable, 0, mouthBoneWithMaskLayerMixerPlayable, 1);

            playableGraph.Connect(eyebrowBoneWithMaskLayerMixerPlayable, 0, faceBoneLayerMixerPlayable, 0);
            playableGraph.Connect(eyesBoneWithMaskLayerMixerPlayable, 0, faceBoneLayerMixerPlayable, 1);
            playableGraph.Connect(mouthBoneWithMaskLayerMixerPlayable, 0, faceBoneLayerMixerPlayable, 2);
            playableGraph.Connect(faceBoneLayerMaskClipPlayable, 0, faceBoneLayerMixerPlayable, 3);

            playableGraph.Connect(faceBoneLayerMixerPlayable, 0, faceBaseLayerMixerPlayable, 0);
            playableGraph.Connect(faceBSLayerMixerPlayable, 0, faceBaseLayerMixerPlayable, 1);

            //playableGraph.Connect(faceAddictiveBlankClipPlayable, 0, faceAddictiveMixerPlayable, 0);
            //playableGraph.Connect(faceAddictiveClipPlayable, 0, faceAddictiveMixerPlayable, 1);

            //playableGraph.Connect(faceBaseLayerMixerPlayable, 0, faceFinalLayerMixerPlayable, 0);
            //playableGraph.Connect(faceAddictiveMixerPlayable, 0, faceFinalLayerMixerPlayable, 1);

            playableGraph.Connect(globalOverWriteLayerMixerPlayable, 0, bodyWithFaceLayerMixerPlayable, 0);
            playableGraph.Connect(faceBaseLayerMixerPlayable, 0, bodyWithFaceLayerMixerPlayable, 1);

            playableGraph.Connect(bodyWithFaceLayerMixerPlayable, 0, debugMaskLayerMixerPlayable, 0);
            playableGraph.Connect(debugMaskBaseClipPlayable, 0, debugMaskLayerMixerPlayable, 1);

            playableGraph.Connect(debugMaskLayerMixerPlayable, 0, poseCatchLayerMixerPlayable, 0);
            playableGraph.Connect(poseCatchAnimPlayable, 0, poseCatchLayerMixerPlayable, 1);

            playableOutput.SetSourcePlayable(poseCatchLayerMixerPlayable);

            playableGraph.Play();
        }

        private void InitAnimSystem()
        {
            // body
            LoadBodyAnimA();

            // breath
            LoadBreathPerformA(initBreathConfig);

            // face
            LoadEyebrowAnimA();
            LoadEyesAnimA();
            LoadMouthAnimA();

            // refresh
            bodyAnimTransUnit.UpdateTransUnit();
            eyebrowAnimTransUnit.UpdateTransUnit();
            eyesAnimTransUnit.UpdateTransUnit();
            mouthAnimTransUnit.UpdateTransUnit();

            UpdateGraphBlendWeight(debugMode);
        }

        public void BodyTransAnim(BodyPerformConfig bodyConfig, bool evaluateImmediately)
        {
            lastBodyConfig = bodyConfig;
            if (bodyConfig.breathPerformConfig.breathAvatarMask == null)
                if (config != null)
                    lastBodyConfig.breathPerformConfig.breathAvatarMask = config.defaultBreathMask;

            if (lastBodyConfig.bodyPerformConfig.playingMode == PlayingMode.BlendLoop)
            {
                lastBodyConfig.bodyPerformConfig.transStaticBlendType = BlendType.Linear;
                lastBodyConfig.bodyPerformConfig.transStaticFullTime = 0;
                lastBodyConfig.breathPerformConfig.breathWeight = 0;
                useLoopBodyTrans = true;
                loopBodyTransEvaluateImmediately = evaluateImmediately;
            }
            else
            {
                useLoopBodyTrans = false;
                ExecuteBodyTrans(lastBodyConfig, evaluateImmediately);
            }
            //readyBodyTrans = true;
        }

        private void LoopBodyTrans(bool evaluateImmediately)
        {
            if ((bodyAnimTransUnit.inAnimA && bodyAnimTransUnit.animASustainTime >= bodyAnimTransUnit.animAEndTime - bodyAnimTransUnit.animAStartTime - bodyAnimTransUnit.transABFullTime) ||
                (bodyAnimTransUnit.inAnimB && bodyAnimTransUnit.animBSustainTime >= bodyAnimTransUnit.animBEndTime - bodyAnimTransUnit.animBStartTime - bodyAnimTransUnit.transABFullTime))
            {
                ExecuteBodyTrans(lastBodyConfig, evaluateImmediately);
            }
        }

        private void ExecuteBodyTrans(BodyPerformConfig bodyConfig, bool evaluateImmediately)
        {
            Debug.Log("ExecuteBodyTrans");
            if (AnimTransSystemIsWorking())
            {
                evaluateImmediately = bodyJustInit && evaluateImmediately;
                if (bodyJustInit)
                {
                    StartCoroutine(ResetBodyJustInitLate());
                    //bodyJustInit = false;
                    bodyConfig.bodyPerformConfig.transABFullTime = 0;
                }

                if (!bodyAnimTransUnit.inAnimA)
                {
                    bodyAnimTransUnit.Trans(bodyConfig.bodyPerformConfig);
                    LoadBodyAnimA();
                    LoadBreathPerformA(bodyConfig.breathPerformConfig);
                }
                if (!bodyAnimTransUnit.inAnimB)
                {
                    bodyAnimTransUnit.Trans(bodyConfig.bodyPerformConfig);
                    LoadBodyAnimB();
                    LoadBreathPerformB(bodyConfig.breathPerformConfig);
                }
                
                if (evaluateImmediately)
                {
                    UpdateSyetem();
                    playableGraph.Evaluate();
                }
            }
        }

        IEnumerator ResetBodyJustInitLate()
        {
            yield return new WaitForEndOfFrame();
            bodyJustInit = false;
        }

        public void EyebrowTransAnim(EyebrowPerformConfig eyebrowConfig)
        {
            lastEyebrowConfig = eyebrowConfig;
            ExecuteEyebrowTrans(lastEyebrowConfig);
            //readyEyebrowTrans = true;
        }
        public void EyesTransAnim(EyesPerformConfig eyesConfig, bool useBlink)
        {
            lastEyesConfig = eyesConfig;

            if (useBlink)
            {
                lastEyesConfig.eyesPerformConfig.transABFullTime = 0;
                lastEyesConfig.eyesPerformConfig.transABBlendType = BlendType.Linear;

                readyEyesTrans = true;
            }
            else
            {
                ExecuteEyesTrans(lastEyesConfig);
                readyEyesTrans = false;
            }

            //if (lastEyesConfig.eyesChangeInsertFrameConfig.useEyesOverwriteAnim && lastEyesConfig.eyesChangeInsertFrameConfig.eyesOverwriteAnim != null)
            //{
            //    lastEyesConfig.eyesPerformConfig.transABFullTime = 0;
            //    lastEyesConfig.eyesPerformConfig.transABBlendType = BlendType.Linear;
            //    lastEyesConfig.eyesChangeInsertFrameConfig.eyesOverwriteAnimFrameTime = Mathf.Clamp(
            //        lastEyesConfig.eyesChangeInsertFrameConfig.eyesOverwriteAnimFrameTime, 
            //        0, 
            //        lastEyesConfig.eyesChangeInsertFrameConfig.eyesOverwriteAnim.length);
            //    LoadEyesChangeInsertFrame();
            //    InitEyesChangeInsertState();
            //}
            //if (lastEyesConfig.eyesChangeInsertFrameConfig.useEyesOverwriteAnim)
            //{
            //    readyEyesTrans = true;
            //}
            //else
            //{
            //    ExecuteEyesTrans(lastEyesConfig);
            //    readyEyesTrans = false;
            //}
        }
        public void MouthTransAnim(MouthPerformConfig mouthConfig)
        {
            lastMouthConfig = mouthConfig;
            ExecuteMouthTrans(lastMouthConfig);
            //readyMouthTrans = true;
        }

        public MouthPerformConfig TransMouthToBlank()
        {
            MouthPerformConfig blankMouthConfig = new MouthPerformConfig();
            if (config != null && config.blankFaceAnimClip != null)
                blankMouthConfig.mouthPerformConfig.animationClip = config.blankFaceAnimClip;
            blankMouthConfig.mouthPerformConfig.startCutTime = 0f;
            blankMouthConfig.mouthPerformConfig.endCutTime = 3f;
            blankMouthConfig.mouthPerformConfig.playingMode = PlayingMode.EndStop;
            blankMouthConfig.mouthPerformConfig.transABBlendType = BlendType.EaseInOut;
            blankMouthConfig.mouthPerformConfig.transABFullTime = 0.3f;
            blankMouthConfig.mouthPerformConfig.transStaticBlendType = BlendType.EaseInOut;
            blankMouthConfig.mouthPerformConfig.transStaticFullTime = 0.3f;
            blankMouthConfig.mouthPerformConfig.weight = 1;
            blankMouthConfig.mouthPerformConfig.playRate = 1;
            blankMouthConfig.mouthPerformConfig.mirror = false;

            MouthPerformConfig prevMouthPerformConfig = lastMouthConfig;
            lastMouthConfig = blankMouthConfig;
            ExecuteMouthTrans(lastMouthConfig);

            return prevMouthPerformConfig;
            //readyMouthTrans = true;
        }

        private void ExecuteEyebrowTrans(EyebrowPerformConfig eyebrowConfig)
        {
            if (AnimTransSystemIsWorking())
            {
                if (eyebrowJustInit)
                {
                    eyebrowConfig.eyebrowPerformConfig.transABFullTime = 0;
                    eyebrowJustInit = false;
                }
                if (!eyebrowAnimTransUnit.inAnimA)
                {
                    eyebrowAnimTransUnit.Trans(eyebrowConfig.eyebrowPerformConfig);
                    LoadEyebrowAnimA();
                }
                if (!eyebrowAnimTransUnit.inAnimB)
                {
                    eyebrowAnimTransUnit.Trans(eyebrowConfig.eyebrowPerformConfig);
                    LoadEyebrowAnimB();
                }
            }
        }

        private void ExecuteEyesTrans(EyesPerformConfig eyesConfig)
        {
            if (AnimTransSystemIsWorking())
            {
                if (eyesJustInit)
                {
                    eyesConfig.eyesPerformConfig.transABFullTime = 0;
                    eyesJustInit = false;
                }
                if (!eyesAnimTransUnit.inAnimA)
                {
                    eyesAnimTransUnit.Trans(eyesConfig.eyesPerformConfig);
                    LoadEyesAnimA();
                }
                if (!eyesAnimTransUnit.inAnimB)
                {
                    eyesAnimTransUnit.Trans(eyesConfig.eyesPerformConfig);
                    LoadEyesAnimB();
                }

            }
        }

        private void ExecuteMouthTrans(MouthPerformConfig mouthConfig)
        {
            if (AnimTransSystemIsWorking())
            {
                if (mouthJustInit)
                {
                    mouthConfig.mouthPerformConfig.transABFullTime = 0;
                    mouthJustInit = false;
                }
                if (!mouthAnimTransUnit.inAnimA)
                {
                    mouthAnimTransUnit.Trans(mouthConfig.mouthPerformConfig);
                    LoadMouthAnimA();
                }
                if (!mouthAnimTransUnit.inAnimB)
                {
                    mouthAnimTransUnit.Trans(mouthConfig.mouthPerformConfig);
                    LoadMouthAnimB();
                }
            }
        }

        public bool ExecuteBlink(EyesOverwritePerformConfig blinkConfig)
        {
            lastBlinkConfig = blinkConfig;

            if (!lastBlinkConfig.eyesOverwriteConfig.useEyesOverwriteAnim)
                return false;
            if (lastBlinkConfig.overwriteType == EyeOverwriteType.None)
                return false;

            if (eyesBlinkAnimLayerWeight == 0)
            {
                switch (lastBlinkConfig.overwriteType)
                {
                    case EyeOverwriteType.Custom:
                        if (lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim == null)
                            if (config != null)
                                lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeClip;
                        break;
                    case EyeOverwriteType.CompletelyClose:
                        if (config != null)
                            lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeClip;
                        break;
                    case EyeOverwriteType.HalfClose:
                        if (config != null)
                            lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeHalfClip;
                        break;
                    default:
                        if (config != null)
                            lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeClip;
                        break;
                }
                if (lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim != null)
                {
                    lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime = Mathf.Clamp(
                        lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime,
                        0,
                        lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim.length);

                    LoadEyesBlinkFrame();
                    InitEyesBlinkState();

                    return true;
                }
            }
            return false;
        }

        public bool ExecuteCloseEyes(EyesOverwritePerformConfig eyesCloseConfig)
        {
            lastEyesCloseConfig = eyesCloseConfig;

            if (!lastEyesCloseConfig.eyesOverwriteConfig.useEyesOverwriteAnim)
                return false;
            if (lastEyesCloseConfig.overwriteType == EyeOverwriteType.None)
                return false;

            switch (lastEyesCloseConfig.overwriteType)
            {
                case EyeOverwriteType.Custom:
                    if (lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim == null)
                        if (config != null)
                            lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeClip;
                    break;
                case EyeOverwriteType.CompletelyClose:
                    if (config != null)
                        lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeClip;
                    break;
                case EyeOverwriteType.HalfClose:
                    if (config != null)
                        lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeHalfClip;
                    break;
                default:
                    if (config != null)
                        lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim = config.defaultCloseEyeClip;
                    break;
            }
            if (lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim != null)
            {
                lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime = Mathf.Clamp(
                    lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime,
                    0,
                    lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim.length);

                lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime = 0;
                LoadEyesCloseFrame();
                InitEyesCloseState();

                return true;
            }
            return false;
        }

        public bool ExecuteOpenEye()
        {
            if (CheckInCloseEyes())
            {
                eyesCloseHoldTimeControlByEvent = false;
                return true;
            }
            return false;
        }

        private void InitEyesBlinkState()
        {
            eyesBlinkDurationTime = 0;
            eyesBlinkInFinish = false;
            eyesBlinkOutFinish = false;
            eyesTransExecuteOnce = false;
        }

        private void InitEyesCloseState()
        {
            eyesCloseDurationTime = 0;
            eyesCloseInFinish = false;
            eyesCloseOutFinish = false;
            eyesCloseHoldTimeControlByEvent = true;
        }

        private void UpdateEyesBlinkState()
        {
            if (lastBlinkConfig.eyesOverwriteConfig.useEyesOverwriteAnim)
            {
                eyesBlinkDurationTime += Time.deltaTime;

                eyesBlinkAnimLayerWeight = eyesBlinkDurationTime <= lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime ?
                    Mathf.Clamp(1 - ((lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime - eyesBlinkDurationTime) / lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime), 0, 1) :
                    Mathf.Clamp(1 - ((eyesBlinkDurationTime - (lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime + lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime)) / lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime), 0, 1);
                if (eyesBlinkDurationTime > lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime)
                    eyesBlinkInFinish = true;
                if (eyesBlinkDurationTime > lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime + lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime + lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime)
                    eyesBlinkOutFinish = true;
            }
        }

        private void UpdateEyesCloseState()
        {
            if (lastEyesCloseConfig.eyesOverwriteConfig.useEyesOverwriteAnim)
            {
                if (!(eyesCloseHoldTimeControlByEvent && eyesCloseDurationTime >= lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime))
                    eyesCloseDurationTime += Time.deltaTime;

                eyesCloseAnimLayerWeight = eyesCloseDurationTime <= lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime ?
                    Mathf.Clamp(1 - ((lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime - eyesCloseDurationTime) / lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime), 0, 1) :
                    Mathf.Clamp(1 - ((eyesCloseDurationTime - (lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime + lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime)) / lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime), 0, 1);
                if (eyesCloseDurationTime > lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime)
                    eyesCloseInFinish = true;
                if (eyesCloseDurationTime > lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime + lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime + lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime)
                    eyesCloseOutFinish = true;
            }
        }

        private void LoadEyesBlinkFrame()
        {
            ChangeAnimationClipPlayableSource(ref eyesWithMaskLayerMixerPlayable, ref eyesBlinkFrameAnimClipPlayable, lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnim, 4);

            eyesBlinkFrameAnimClipPlayable.SetTime(lastBlinkConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime);
            eyesBlinkFrameAnimClipPlayable.Pause();

            eyesWithMaskLayerMixerPlayable.SetInputWeight(4, 0);
        }

        private void LoadEyesCloseFrame()
        {
            ChangeAnimationClipPlayableSource(ref eyesWithMaskLayerMixerPlayable, ref eyesCloseFrameAnimClipPlayable, lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim, 3);

            eyesCloseFrameAnimClipPlayable.SetTime(lastEyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime);
            eyesCloseFrameAnimClipPlayable.Pause();

            eyesWithMaskLayerMixerPlayable.SetInputWeight(3, 0);
        }

        private void LoadBodyAnimA()
        {
            if (bodyAnimTransUnit.animAClip != null)
            {
                if (bodyAnimTransUnit.playingMode == PlayingMode.EndStop || bodyAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    animAPlayingController["TempClip"] = bodyAnimTransUnit.animAClip;
                    animAPlayingControllerPlayable.PlayInFixedTime("Base Layer.Clip", 0, bodyAnimTransUnit.animAStartTime);

                    animAStaticController["TempClip"] = bodyAnimTransUnit.animAClip;
                    animAStaticControllerPlayable.PlayInFixedTime("Base Layer.Clip", 0, bodyAnimTransUnit.animAEndTime);
                    animAStaticControllerPlayable.SetFloat("EndPosition", bodyAnimTransUnit.animAEndNormalizedTime);

                    animASwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    animASwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (bodyAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    animALoopController["TempClip"] = bodyAnimTransUnit.animAClip;
                    animALoopControllerPlayable.PlayInFixedTime("Base Layer.Clip", 0, bodyAnimTransUnit.animAStartTime);

                    animASwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    animASwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }

                animAPlayingControllerPlayable.SetFloat("Speed", bodyAnimTransUnit.animAPlayRate);
                animAPlayingControllerPlayable.SetBool("Mirror", bodyAnimTransUnit.AnimAMirror);
                animAStaticControllerPlayable.SetBool("Mirror", bodyAnimTransUnit.AnimAMirror);
            }
        }

        private void LoadBodyAnimB()
        {
            if (bodyAnimTransUnit.animBClip != null)
            {
                if (bodyAnimTransUnit.playingMode == PlayingMode.EndStop || bodyAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    animBPlayingController["TempClip"] = bodyAnimTransUnit.animBClip;
                    animBPlayingControllerPlayable.PlayInFixedTime("Base Layer.Clip", 0, bodyAnimTransUnit.animBStartTime);

                    animBStaticController["TempClip"] = bodyAnimTransUnit.animBClip;
                    animBStaticControllerPlayable.PlayInFixedTime("Base Layer.Clip", 0, bodyAnimTransUnit.animBEndTime);
                    animBStaticControllerPlayable.SetFloat("EndPosition", bodyAnimTransUnit.animBEndNormalizedTime);

                    animBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    animBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (bodyAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    animBLoopController["TempClip"] = bodyAnimTransUnit.animBClip;
                    animBLoopControllerPlayable.PlayInFixedTime("Base Layer.Clip", 0, bodyAnimTransUnit.animBStartTime);

                    animBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    animBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }

                animBPlayingControllerPlayable.SetFloat("Speed", bodyAnimTransUnit.animBPlayRate);
                animBPlayingControllerPlayable.SetBool("Mirror", bodyAnimTransUnit.AnimBMirror);
                animBStaticControllerPlayable.SetBool("Mirror", bodyAnimTransUnit.AnimBMirror);
            }
        }

        private void LoadEyebrowAnimA()
        {
            if (eyebrowAnimTransUnit.animAClip != null)
            {
                if (/*eyebrowAnimTransUnit.playingMode == PlayingMode.LoopThenStop || */eyebrowAnimTransUnit.playingMode == PlayingMode.EndStop || eyebrowAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref eyebrowAnimALayerMixerPlayable, ref eyebrowAnimAClipPlayable, eyebrowAnimTransUnit.animAClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyebrowAnimALayerMixerPlayable, ref eyebrowAnimAStaticClipPlayable, eyebrowAnimTransUnit.animAClip, 1);
                    
                    eyebrowAnimAClipPlayable.SetTime(eyebrowAnimTransUnit.animAStartTime);
                    eyebrowAnimAClipPlayable.Play();
                    eyebrowAnimAStaticClipPlayable.SetTime(eyebrowAnimTransUnit.animAEndTime);
                    eyebrowAnimAStaticClipPlayable.Pause();

                    eyebrowAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyebrowAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref eyebrowBoneAnimALayerMixerPlayable, ref eyebrowBoneAnimAClipPlayable, eyebrowAnimTransUnit.animAClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyebrowBoneAnimALayerMixerPlayable, ref eyebrowBoneAnimAStaticClipPlayable, eyebrowAnimTransUnit.animAClip, 1);
                    
                    eyebrowBoneAnimAClipPlayable.SetTime(eyebrowAnimTransUnit.animAStartTime);
                    eyebrowBoneAnimAClipPlayable.Play();
                    eyebrowBoneAnimAStaticClipPlayable.SetTime(eyebrowAnimTransUnit.animAEndTime);
                    eyebrowBoneAnimAStaticClipPlayable.Pause();

                    eyebrowBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyebrowBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (eyebrowAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    //BS
                    ChangeAnimationClipPlayableSource(ref eyebrowAnimASwitchPlayingAndLoopPlayable, ref eyebrowAnimALoopClipPlayable, eyebrowAnimTransUnit.animAClip, 1);

                    eyebrowAnimALoopClipPlayable.SetTime(0);
                    eyebrowAnimALoopClipPlayable.Play();

                    eyebrowAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyebrowAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);

                    //Bone
                    ChangeAnimationClipPlayableSource(ref eyebrowBoneAnimASwitchPlayingAndLoopPlayable, ref eyebrowBoneAnimALoopClipPlayable, eyebrowAnimTransUnit.animAClip, 1);

                    eyebrowBoneAnimALoopClipPlayable.SetTime(0);
                    eyebrowBoneAnimALoopClipPlayable.Play();

                    eyebrowBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyebrowBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }
            }
        }

        private void LoadEyebrowAnimB()
        {
            if (eyebrowAnimTransUnit.animBClip != null)
            {
                if (/*eyebrowAnimTransUnit.playingMode == PlayingMode.LoopThenStop || */eyebrowAnimTransUnit.playingMode == PlayingMode.EndStop || eyebrowAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref eyebrowAnimBLayerMixerPlayable, ref eyebrowAnimBClipPlayable, eyebrowAnimTransUnit.animBClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyebrowAnimBLayerMixerPlayable, ref eyebrowAnimBStaticClipPlayable, eyebrowAnimTransUnit.animBClip, 1);
                    
                    eyebrowAnimBClipPlayable.SetTime(eyebrowAnimTransUnit.animBStartTime);
                    eyebrowAnimBClipPlayable.Play();
                    eyebrowAnimBStaticClipPlayable.SetTime(eyebrowAnimTransUnit.animBEndTime);
                    eyebrowAnimBStaticClipPlayable.Pause();

                    eyebrowAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyebrowAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref eyebrowBoneAnimBLayerMixerPlayable, ref eyebrowBoneAnimBClipPlayable, eyebrowAnimTransUnit.animBClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyebrowBoneAnimBLayerMixerPlayable, ref eyebrowBoneAnimBStaticClipPlayable, eyebrowAnimTransUnit.animBClip, 1);

                    eyebrowBoneAnimBClipPlayable.SetTime(eyebrowAnimTransUnit.animBStartTime);
                    eyebrowBoneAnimBClipPlayable.Play();
                    eyebrowBoneAnimBStaticClipPlayable.SetTime(eyebrowAnimTransUnit.animBEndTime);
                    eyebrowBoneAnimBStaticClipPlayable.Pause();

                    eyebrowBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyebrowBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (eyebrowAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref eyebrowAnimBSwitchPlayingAndLoopPlayable, ref eyebrowAnimBLoopClipPlayable, eyebrowAnimTransUnit.animBClip, 1);

                    eyebrowAnimBLoopClipPlayable.SetTime(0);
                    eyebrowAnimBLoopClipPlayable.Play();

                    eyebrowAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyebrowAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref eyebrowBoneAnimBSwitchPlayingAndLoopPlayable, ref eyebrowBoneAnimBLoopClipPlayable, eyebrowAnimTransUnit.animBClip, 1);

                    eyebrowBoneAnimBLoopClipPlayable.SetTime(0);
                    eyebrowBoneAnimBLoopClipPlayable.Play();

                    eyebrowBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyebrowBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }
            }
        }

        private void LoadEyesAnimA()
        {
            if (eyesAnimTransUnit.animAClip != null)
            {
                if (/*eyesAnimTransUnit.playingMode == PlayingMode.LoopThenStop || */eyesAnimTransUnit.playingMode == PlayingMode.EndStop || eyesAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref eyesAnimALayerMixerPlayable, ref eyesAnimAClipPlayable, eyesAnimTransUnit.animAClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyesAnimALayerMixerPlayable, ref eyesAnimAStaticClipPlayable, eyesAnimTransUnit.animAClip, 1);

                    eyesAnimAClipPlayable.SetTime(eyesAnimTransUnit.animAStartTime);
                    eyesAnimAClipPlayable.Play();
                    eyesAnimAStaticClipPlayable.SetTime(eyesAnimTransUnit.animAEndTime);
                    eyesAnimAStaticClipPlayable.Pause();

                    eyesAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyesAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref eyesBoneAnimALayerMixerPlayable, ref eyesBoneAnimAClipPlayable, eyesAnimTransUnit.animAClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyesBoneAnimALayerMixerPlayable, ref eyesBoneAnimAStaticClipPlayable, eyesAnimTransUnit.animAClip, 1);
                    
                    eyesBoneAnimAClipPlayable.SetTime(eyesAnimTransUnit.animAStartTime);
                    eyesBoneAnimAClipPlayable.Play();
                    eyesBoneAnimAStaticClipPlayable.SetTime(eyesAnimTransUnit.animAEndTime);
                    eyesBoneAnimAStaticClipPlayable.Pause();

                    eyesBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyesBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (eyesAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref eyesAnimASwitchPlayingAndLoopPlayable, ref eyesAnimALoopClipPlayable, eyesAnimTransUnit.animAClip, 1);

                    eyesAnimALoopClipPlayable.SetTime(0);
                    eyesAnimALoopClipPlayable.Play();

                    eyesAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyesAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref eyesBoneAnimASwitchPlayingAndLoopPlayable, ref eyesBoneAnimALoopClipPlayable, eyesAnimTransUnit.animAClip, 1);

                    eyesBoneAnimALoopClipPlayable.SetTime(0);
                    eyesBoneAnimALoopClipPlayable.Play();

                    eyesBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyesBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }
            }
        }

        private void LoadEyesAnimB()
        {
            if (eyesAnimTransUnit.animBClip != null)
            {
                if (/*eyesAnimTransUnit.playingMode == PlayingMode.LoopThenStop ||*/ eyesAnimTransUnit.playingMode == PlayingMode.EndStop || eyesAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref eyesAnimBLayerMixerPlayable, ref eyesAnimBClipPlayable, eyesAnimTransUnit.animBClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyesAnimBLayerMixerPlayable, ref eyesAnimBStaticClipPlayable, eyesAnimTransUnit.animBClip, 1);
                    
                    eyesAnimBClipPlayable.SetTime(eyesAnimTransUnit.animBStartTime);
                    eyesAnimBClipPlayable.Play();
                    eyesAnimBStaticClipPlayable.SetTime(eyesAnimTransUnit.animBEndTime);
                    eyesAnimBStaticClipPlayable.Pause();

                    eyesAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyesAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref eyesBoneAnimBLayerMixerPlayable, ref eyesBoneAnimBClipPlayable, eyesAnimTransUnit.animBClip, 0);
                    ChangeAnimationClipPlayableSource(ref eyesBoneAnimBLayerMixerPlayable, ref eyesBoneAnimBStaticClipPlayable, eyesAnimTransUnit.animBClip, 1);
                    
                    eyesBoneAnimBClipPlayable.SetTime(eyesAnimTransUnit.animBStartTime);
                    eyesBoneAnimBClipPlayable.Play();
                    eyesBoneAnimBStaticClipPlayable.SetTime(eyesAnimTransUnit.animBEndTime);
                    eyesBoneAnimBStaticClipPlayable.Pause();

                    eyesBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    eyesBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (eyesAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref eyesAnimBSwitchPlayingAndLoopPlayable, ref eyesAnimBLoopClipPlayable, eyesAnimTransUnit.animBClip, 1);

                    eyesAnimBLoopClipPlayable.SetTime(0);
                    eyesAnimBLoopClipPlayable.Play();

                    eyesAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyesAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref eyesBoneAnimBSwitchPlayingAndLoopPlayable, ref eyesBoneAnimBLoopClipPlayable, eyesAnimTransUnit.animBClip, 1);

                    eyesBoneAnimBLoopClipPlayable.SetTime(0);
                    eyesBoneAnimBLoopClipPlayable.Play();

                    eyesBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    eyesBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }
            }
        }

        private void LoadMouthAnimA()
        {
            if (mouthAnimTransUnit.animAClip != null)
            {
                if (/*mouthAnimTransUnit.playingMode == PlayingMode.LoopThenStop ||*/ mouthAnimTransUnit.playingMode == PlayingMode.EndStop || mouthAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref mouthAnimALayerMixerPlayable, ref mouthAnimAClipPlayable, mouthAnimTransUnit.animAClip, 0);
                    ChangeAnimationClipPlayableSource(ref mouthAnimALayerMixerPlayable, ref mouthAnimAStaticClipPlayable, mouthAnimTransUnit.animAClip, 1);
                    
                    mouthAnimAClipPlayable.SetTime(mouthAnimTransUnit.animAStartTime);
                    mouthAnimAClipPlayable.Play();
                    mouthAnimAStaticClipPlayable.SetTime(mouthAnimTransUnit.animAEndTime);
                    mouthAnimAStaticClipPlayable.Pause();

                    mouthAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    mouthAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref mouthBoneAnimALayerMixerPlayable, ref mouthBoneAnimAClipPlayable, mouthAnimTransUnit.animAClip, 0);
                    ChangeAnimationClipPlayableSource(ref mouthBoneAnimALayerMixerPlayable, ref mouthBoneAnimAStaticClipPlayable, mouthAnimTransUnit.animAClip, 1);
                    
                    mouthBoneAnimAClipPlayable.SetTime(mouthAnimTransUnit.animAStartTime);
                    mouthBoneAnimAClipPlayable.Play();
                    mouthBoneAnimAStaticClipPlayable.SetTime(mouthAnimTransUnit.animAEndTime);
                    mouthBoneAnimAStaticClipPlayable.Pause();

                    mouthBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    mouthBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (mouthAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref mouthAnimASwitchPlayingAndLoopPlayable, ref mouthAnimALoopClipPlayable, mouthAnimTransUnit.animAClip, 1);

                    mouthAnimALoopClipPlayable.SetTime(0);
                    mouthAnimALoopClipPlayable.Play();

                    mouthAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    mouthAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref mouthBoneAnimASwitchPlayingAndLoopPlayable, ref mouthBoneAnimALoopClipPlayable, mouthAnimTransUnit.animAClip, 1);

                    mouthBoneAnimALoopClipPlayable.SetTime(0);
                    mouthBoneAnimALoopClipPlayable.Play();

                    mouthBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    mouthBoneAnimASwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }
            }
        }

        private void LoadMouthAnimB()
        {
            if (mouthAnimTransUnit.animBClip != null)
            {
                if (/*mouthAnimTransUnit.playingMode == PlayingMode.LoopThenStop ||*/ mouthAnimTransUnit.playingMode == PlayingMode.EndStop || mouthAnimTransUnit.playingMode == PlayingMode.BlendLoop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref mouthAnimBLayerMixerPlayable, ref mouthAnimBClipPlayable, mouthAnimTransUnit.animBClip, 0);
                    ChangeAnimationClipPlayableSource(ref mouthAnimBLayerMixerPlayable, ref mouthAnimBStaticClipPlayable, mouthAnimTransUnit.animBClip, 1);

                    mouthAnimBClipPlayable.SetTime(mouthAnimTransUnit.animBStartTime);
                    mouthAnimBClipPlayable.Play();
                    mouthAnimBStaticClipPlayable.SetTime(mouthAnimTransUnit.animBEndTime);
                    mouthAnimBStaticClipPlayable.Pause();

                    mouthAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    mouthAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref mouthBoneAnimBLayerMixerPlayable, ref mouthBoneAnimBClipPlayable, mouthAnimTransUnit.animBClip, 0);
                    ChangeAnimationClipPlayableSource(ref mouthBoneAnimBLayerMixerPlayable, ref mouthBoneAnimBStaticClipPlayable, mouthAnimTransUnit.animBClip, 1);
                    
                    mouthBoneAnimBClipPlayable.SetTime(mouthAnimTransUnit.animBStartTime);
                    mouthBoneAnimBClipPlayable.Play();
                    mouthBoneAnimBStaticClipPlayable.SetTime(mouthAnimTransUnit.animBEndTime);
                    mouthBoneAnimBStaticClipPlayable.Pause();

                    mouthBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 1);
                    mouthBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 0);
                }
                else if (mouthAnimTransUnit.playingMode == PlayingMode.Loop)
                {
                    // BS
                    ChangeAnimationClipPlayableSource(ref mouthAnimBSwitchPlayingAndLoopPlayable, ref mouthAnimBLoopClipPlayable, mouthAnimTransUnit.animBClip, 1);
                    
                    mouthAnimBLoopClipPlayable.SetTime(0);
                    mouthAnimBLoopClipPlayable.Play();

                    mouthAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    mouthAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);

                    // Bone
                    ChangeAnimationClipPlayableSource(ref mouthBoneAnimBSwitchPlayingAndLoopPlayable, ref mouthBoneAnimBLoopClipPlayable, mouthAnimTransUnit.animBClip, 1);
                    
                    mouthBoneAnimBLoopClipPlayable.SetTime(0);
                    mouthBoneAnimBLoopClipPlayable.Play();

                    mouthBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(0, 0);
                    mouthBoneAnimBSwitchPlayingAndLoopPlayable.SetInputWeight(1, 1);
                }
            }
        }

        private void LoadBreathPerformA(BreathPerformConfig breathConfig)
        {
            if (breathConfig.breathClip != null)
            {
                animABreathPerformConfig = breathConfig;
                ChangeAnimationClipPlayableSource(ref animABreathLayerMixerPlayable, ref breathPlayableA, animABreathPerformConfig.breathClip, 1);
                if (useRandomBreath)
                    breathPlayableA.SetTime(UnityEngine.Random.Range(0, breathConfig.breathClip.length));

                if (animABreathPerformConfig.breathAvatarMask != null)
                    animABreathLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, animABreathPerformConfig.breathAvatarMask);
            }
        }

        private void LoadBreathPerformB(BreathPerformConfig breathConfig)
        {
            if (breathConfig.breathClip != null)
            {
                animBBreathPerformConfig = breathConfig;
                ChangeAnimationClipPlayableSource(ref animBBreathLayerMixerPlayable, ref breathPlayableB, animBBreathPerformConfig.breathClip, 1);
                if (useRandomBreath)
                    breathPlayableB.SetTime(UnityEngine.Random.Range(0, breathConfig.breathClip.length));

                if (animBBreathPerformConfig.breathAvatarMask != null)
                    animBBreathLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, animBBreathPerformConfig.breathAvatarMask);
            }
        }

        public void OverWriteLocalHand(bool overWriteRightHand, bool overWriteLeftHand, AnimationClip rightHandClip, AnimationClip leftHandClip)
        {
            if (AnimTransSystemIsWorking())
            {
                if (overWriteRightHand && rightHandOverWriteMaskIsOK && rightHandClip != null)
                {
                    if (!bodyAnimTransUnit.inAnimA)
                    {
                        LoadAnimARightHand(rightHandClip);
                        animALocalOverWriteLayerMixerPlayable.SetInputWeight(1, 1);
                    }
                    if (!bodyAnimTransUnit.inAnimB)
                    {
                        LoadAnimBRightHand(rightHandClip);
                        animBLocalOverWriteLayerMixerPlayable.SetInputWeight(1, 1);
                    }
                }
                else
                {
                    if (!bodyAnimTransUnit.inAnimA)
                        animALocalOverWriteLayerMixerPlayable.SetInputWeight(1, 0);
                    if (!bodyAnimTransUnit.inAnimB)
                        animBLocalOverWriteLayerMixerPlayable.SetInputWeight(1, 0);
                }

                if (overWriteLeftHand && leftHandOverWriteMaskIsOK && leftHandClip != null)
                {
                    if (!bodyAnimTransUnit.inAnimA)
                    {
                        LoadAnimALeftHand(leftHandClip);
                        animALocalOverWriteLayerMixerPlayable.SetInputWeight(2, 1);
                    }
                    if (!bodyAnimTransUnit.inAnimB)
                    {
                        LoadAnimBLeftHand(leftHandClip);
                        animBLocalOverWriteLayerMixerPlayable.SetInputWeight(2, 1);
                    }
                }
                else
                {
                    if (!bodyAnimTransUnit.inAnimA)
                        animALocalOverWriteLayerMixerPlayable.SetInputWeight(2, 0);
                    if (!bodyAnimTransUnit.inAnimB)
                        animBLocalOverWriteLayerMixerPlayable.SetInputWeight(2, 0);
                }
            }
        }

        private void LoadAnimARightHand(AnimationClip rightHandClip)
        {
            if (rightHandClip != null)
            {
                localAnimARightHandClip = rightHandClip;
                ChangeAnimationClipPlayableSource(ref animALocalOverWriteLayerMixerPlayable, ref animARightHandLocalOverWriteClipPlayable, localAnimARightHandClip, 1);
            }
        }
        private void LoadAnimALeftHand(AnimationClip leftHandClip)
        {
            if (leftHandClip != null)
            {
                localAnimALeftHandClip = leftHandClip;
                ChangeAnimationClipPlayableSource(ref animALocalOverWriteLayerMixerPlayable, ref animALeftHandLocalOverWriteClipPlayable, localAnimALeftHandClip, 2);
            }
        }
        private void LoadAnimBRightHand(AnimationClip rightHandClip)
        {
            if (rightHandClip != null)
            {
                localAnimBRightHandClip = rightHandClip;
                ChangeAnimationClipPlayableSource(ref animBLocalOverWriteLayerMixerPlayable, ref animBRightHandLocalOverWriteClipPlayable, localAnimBRightHandClip, 1);
            }
        }
        private void LoadAnimBLeftHand(AnimationClip leftHandClip)
        {
            if (leftHandClip != null)
            {
                localAnimBLeftHandClip = leftHandClip;
                ChangeAnimationClipPlayableSource(ref animBLocalOverWriteLayerMixerPlayable, ref animBLeftHandLocalOverWriteClipPlayable, localAnimBLeftHandClip, 2);
            }
        }

        public void OverWriteGlobalHand(bool overWriteRightHand, bool overWriteLeftHand, AnimationClip rightHandClip, AnimationClip leftHandClip)
        {
            OverWriteRightGlobalHand(overWriteRightHand, rightHandClip);
            OverWriteLeftGlobalHand(overWriteLeftHand, leftHandClip);
        }

        public void OverWriteRightGlobalHand(bool overWriteRightHand, AnimationClip rightHandClip)
        {
            if (AnimTransSystemIsWorking())
            {
                if (overWriteRightHand && rightHandOverWriteMaskIsOK && rightHandClip != null)
                {
                    LoadGlobalRightHand(rightHandClip);
                    globalOverWriteLayerMixerPlayable.SetInputWeight(1, 1);
                }
                else
                {
                    globalOverWriteLayerMixerPlayable.SetInputWeight(1, 0);
                }
            }
        }

        public void OverWriteLeftGlobalHand(bool overWriteLeftHand, AnimationClip leftHandClip)
        {
            if (AnimTransSystemIsWorking())
            {
                if (overWriteLeftHand && leftHandOverWriteMaskIsOK && leftHandClip != null)
                {
                    LoadGlobalLeftHand(leftHandClip);
                    globalOverWriteLayerMixerPlayable.SetInputWeight(2, 1);
                }
                else
                {
                    globalOverWriteLayerMixerPlayable.SetInputWeight(2, 0);
                }
            }
        }

        private void LoadGlobalRightHand(AnimationClip rightHandClip)
        {
            if (rightHandClip != null)
            {
                globalRightHandClip = rightHandClip;
                ChangeAnimationClipPlayableSource(ref globalOverWriteLayerMixerPlayable, ref rightHandGlobalOverWriteClipPlayable, globalRightHandClip, 1);
                rightHandGlobalOverWriteClipPlayable.SetTime(0);
                rightHandGlobalOverWriteClipPlayable.Play();
            }
        }
        private void LoadGlobalLeftHand(AnimationClip leftHandClip)
        {
            if (leftHandClip != null)
            {
                globalLeftHandClip = leftHandClip;
                ChangeAnimationClipPlayableSource(ref globalOverWriteLayerMixerPlayable, ref leftHandGlobalOverWriteClipPlayable, globalLeftHandClip, 2);
                leftHandGlobalOverWriteClipPlayable.SetTime(0);
                leftHandGlobalOverWriteClipPlayable.Play();
            }
        }
        
        public GameObject ExecuteCatchPose(StationTemplatePostureType postureType, AnimationClip custromPoseCatchAnimClip, float customPoseCatchAnimTime, Func<GameObject, GameObject> func)
        {
            if (AnimTransSystemIsWorking() && func != null && config != null)
            {
                switch (postureType)
                {
                    case StationTemplatePostureType.Null:
                        poseCatchAnimClip = config.defaultStandIdleClip;
                        poseCatchAnimTime = 0;
                        break;
                    case StationTemplatePostureType.Stand:
                        poseCatchAnimClip = config.defaultStandIdleClip;
                        poseCatchAnimTime = 0;
                        break;
                    case StationTemplatePostureType.Sit:
                        poseCatchAnimClip = config.defaultSitIdleClip;
                        poseCatchAnimTime = 0;
                        break;
                    case StationTemplatePostureType.Custom:
                        if (custromPoseCatchAnimClip != null)
                        {
                            poseCatchAnimClip = custromPoseCatchAnimClip;
                            poseCatchAnimTime = Mathf.Clamp(customPoseCatchAnimTime, 0, custromPoseCatchAnimClip.length);
                        }
                        else
                        {
                            poseCatchAnimClip = config.defaultStandIdleClip;
                            poseCatchAnimTime = 0;
                        }
                        break;
                    default:
                        poseCatchAnimClip = config.defaultStandIdleClip;
                        poseCatchAnimTime = 0;
                        break;
                }

                if (poseCatchAnimClip != null)
                {
                    ChangeAnimationClipPlayableSource(ref poseCatchLayerMixerPlayable, ref poseCatchAnimPlayable, poseCatchAnimClip, 1);
                    poseCatchAnimPlayable.SetTime(poseCatchAnimTime);
                    poseCatchAnimPlayable.Pause();
                    poseCatchLayerMixerPlayable.SetInputWeight(1, 1);
                    float oriOutputWeight = playableGraph.GetOutput(0).GetWeight();
                    playableGraph.GetOutput(0).SetWeight(1);
                    playableGraph.Evaluate();

                    // do something
                    GameObject obj = func(this.gameObject);

                    poseCatchLayerMixerPlayable.SetInputWeight(1, 0);
                    playableGraph.GetOutput(0).SetWeight(oriOutputWeight);
                    playableGraph.Evaluate();

                    return obj;
                }
            }
            return null;
        }

        //public void ChangeGlobalAddictiveWeight(float weight)
        //{
        //    if (AnimTransSystemIsWorking())
        //    {
        //        globalAddictiveWeight = weight;
        //    }
        //}

        //public void ChangeGlobalAddictiveMask(AvatarMask newMask)
        //{
        //    if (AnimTransSystemIsWorking())
        //    {
        //        if (newMask != null)
        //        {
        //            globalAddictiveMask = newMask;
        //            globalAddictiveLayerMixerPlayable.SetLayerMaskFromAvatarMask(1, globalAddictiveMask);
        //        }
        //    }
        //}

        //public void ChangeGlobalAddictiveAnimClip(AnimationClip animClip)
        //{
        //    if (AnimTransSystemIsWorking())
        //    {
        //        if (animClip != null)
        //        {
        //            globalAddictiveClip = animClip;
        //            playableGraph.Disconnect(globalAddictiveLayerMixerPlayable, 1);
        //            globalAddictiveClipPlayable = AnimationClipPlayable.Create(playableGraph, globalAddictiveClip);
        //            playableGraph.Connect(globalAddictiveClipPlayable, 0, globalAddictiveLayerMixerPlayable, 1);
        //        }
        //    }
        //}

        //public void ChangeFaceAddictiveAnimClip(AnimationClip animClip)
        //{
        //    if (AnimTransSystemIsWorking())
        //    {
        //        if (animClip != null)
        //        {
        //            faceAddictiveClip = animClip;
        //            playableGraph.Disconnect(faceAddictiveMixerPlayable, 1);
        //            faceAddictiveClipPlayable = AnimationClipPlayable.Create(playableGraph, faceAddictiveClip);
        //            playableGraph.Connect(faceAddictiveClipPlayable, 0, faceAddictiveMixerPlayable, 1);
        //        }
        //    }
        //}

        private void UpdateGraphBlendWeight(bool isDebugMode)
        {
            if (isDebugMode == false)
            {
                animAMixerPlayable.SetInputWeight(0, 1 - bodyAnimTransUnit.animAStaticTransWeight);
                animAMixerPlayable.SetInputWeight(1, bodyAnimTransUnit.animAStaticTransWeight);

                animBMixerPlayable.SetInputWeight(0, 1 - bodyAnimTransUnit.animBStaticTransWeight);
                animBMixerPlayable.SetInputWeight(1, bodyAnimTransUnit.animBStaticTransWeight);

                transMixerPlayable.SetInputWeight(0, 1 - bodyAnimTransUnit.animABTransWeight);
                transMixerPlayable.SetInputWeight(1, bodyAnimTransUnit.animABTransWeight);

                animABreathLayerMixerPlayable.SetInputWeight(1, Mathf.Clamp01(animABreathPerformConfig.breathWeight));
                animBBreathLayerMixerPlayable.SetInputWeight(1, Mathf.Clamp01(animBBreathPerformConfig.breathWeight));

                //globalAddictiveLayerMixerPlayable.SetInputWeight(1, Mathf.Clamp01(globalAddictiveWeight));

                debugMaskLayerMixerPlayable.SetInputWeight(1, 0);
            }
            else
            {
                if (showDebugStep)
                {
                    float A2BWeight = (float)debugStep / (fullDebugStep - 1);
                    if (!bodyAnimTransUnit.transA2B)
                        A2BWeight = 1 - A2BWeight;

                    animAMixerPlayable.SetInputWeight(0, 0);
                    animAMixerPlayable.SetInputWeight(1, 1);

                    animBMixerPlayable.SetInputWeight(0, 0);
                    animBMixerPlayable.SetInputWeight(1, 1);

                    transMixerPlayable.SetInputWeight(0, 1 - A2BWeight);
                    transMixerPlayable.SetInputWeight(1, A2BWeight);

                    animABreathLayerMixerPlayable.SetInputWeight(1, 0);
                    animBBreathLayerMixerPlayable.SetInputWeight(1, 0);

                    //globalAddictiveLayerMixerPlayable.SetInputWeight(1, 0);

                    debugMaskLayerMixerPlayable.SetInputWeight(1, 1);
                }
                else
                {
                    animAMixerPlayable.SetInputWeight(0, 1 - bodyAnimTransUnit.animAStaticTransWeight);
                    animAMixerPlayable.SetInputWeight(1, bodyAnimTransUnit.animAStaticTransWeight);

                    animBMixerPlayable.SetInputWeight(0, 1 - bodyAnimTransUnit.animBStaticTransWeight);
                    animBMixerPlayable.SetInputWeight(1, bodyAnimTransUnit.animBStaticTransWeight);

                    transMixerPlayable.SetInputWeight(0, 1 - bodyAnimTransUnit.animABTransWeight);
                    transMixerPlayable.SetInputWeight(1, bodyAnimTransUnit.animABTransWeight);

                    animABreathLayerMixerPlayable.SetInputWeight(1, Mathf.Clamp01(animABreathPerformConfig.breathWeight));
                    animBBreathLayerMixerPlayable.SetInputWeight(1, Mathf.Clamp01(animBBreathPerformConfig.breathWeight));

                    //globalAddictiveLayerMixerPlayable.SetInputWeight(1, Mathf.Clamp01(globalAddictiveWeight));

                    debugMaskLayerMixerPlayable.SetInputWeight(1, 1);
                }
            }

            eyebrowAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowAnimALayerMixerPlayable.SetInputWeight(1, eyebrowAnimTransUnit.animAStaticTransWeight);
            eyesAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyesAnimALayerMixerPlayable.SetInputWeight(1, eyesAnimTransUnit.animAStaticTransWeight);
            mouthAnimALayerMixerPlayable.SetInputWeight(0, 1);
            mouthAnimALayerMixerPlayable.SetInputWeight(1, mouthAnimTransUnit.animAStaticTransWeight);
            eyebrowAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowAnimBLayerMixerPlayable.SetInputWeight(1, eyebrowAnimTransUnit.animBStaticTransWeight);
            eyesAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyesAnimBLayerMixerPlayable.SetInputWeight(1, eyesAnimTransUnit.animBStaticTransWeight);
            mouthAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            mouthAnimBLayerMixerPlayable.SetInputWeight(1, mouthAnimTransUnit.animBStaticTransWeight);

            eyebrowBoneAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowBoneAnimALayerMixerPlayable.SetInputWeight(1, eyebrowAnimTransUnit.animAStaticTransWeight);
            eyesBoneAnimALayerMixerPlayable.SetInputWeight(0, 1);
            eyesBoneAnimALayerMixerPlayable.SetInputWeight(1, eyesAnimTransUnit.animAStaticTransWeight);
            mouthBoneAnimALayerMixerPlayable.SetInputWeight(0, 1);
            mouthBoneAnimALayerMixerPlayable.SetInputWeight(1, mouthAnimTransUnit.animAStaticTransWeight);
            eyebrowBoneAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyebrowBoneAnimBLayerMixerPlayable.SetInputWeight(1, eyebrowAnimTransUnit.animBStaticTransWeight);
            eyesBoneAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            eyesBoneAnimBLayerMixerPlayable.SetInputWeight(1, eyesAnimTransUnit.animBStaticTransWeight);
            mouthBoneAnimBLayerMixerPlayable.SetInputWeight(0, 1);
            mouthBoneAnimBLayerMixerPlayable.SetInputWeight(1, mouthAnimTransUnit.animBStaticTransWeight);

            eyebrowWithMaskLayerMixerPlayable.SetInputWeight(1, (1 - eyebrowAnimTransUnit.animABTransWeight) * eyebrowAnimTransUnit.weightLerpAB);
            eyebrowWithMaskLayerMixerPlayable.SetInputWeight(2, eyebrowAnimTransUnit.animABTransWeight * eyebrowAnimTransUnit.weightLerpAB);
            eyesWithMaskLayerMixerPlayable.SetInputWeight(1, (1 - eyesAnimTransUnit.animABTransWeight) * eyesAnimTransUnit.weightLerpAB);
            eyesWithMaskLayerMixerPlayable.SetInputWeight(2, eyesAnimTransUnit.animABTransWeight * eyesAnimTransUnit.weightLerpAB);
            mouthWithMaskLayerMixerPlayable.SetInputWeight(1, (1 - mouthAnimTransUnit.animABTransWeight) * mouthAnimTransUnit.weightLerpAB);
            mouthWithMaskLayerMixerPlayable.SetInputWeight(2, mouthAnimTransUnit.animABTransWeight * mouthAnimTransUnit.weightLerpAB);

            eyebrowBoneWithMaskLayerMixerPlayable.SetInputWeight(0, (1 - eyebrowAnimTransUnit.animABTransWeight) * eyebrowAnimTransUnit.weightLerpAB);
            eyebrowBoneWithMaskLayerMixerPlayable.SetInputWeight(1, eyebrowAnimTransUnit.animABTransWeight * eyebrowAnimTransUnit.weightLerpAB);
            eyesBoneWithMaskLayerMixerPlayable.SetInputWeight(0, (1 - eyesAnimTransUnit.animABTransWeight) * eyesAnimTransUnit.weightLerpAB);
            eyesBoneWithMaskLayerMixerPlayable.SetInputWeight(1, eyesAnimTransUnit.animABTransWeight * eyesAnimTransUnit.weightLerpAB);
            mouthBoneWithMaskLayerMixerPlayable.SetInputWeight(0, (1 - mouthAnimTransUnit.animABTransWeight) * mouthAnimTransUnit.weightLerpAB);
            mouthBoneWithMaskLayerMixerPlayable.SetInputWeight(1, mouthAnimTransUnit.animABTransWeight * mouthAnimTransUnit.weightLerpAB);

            eyesWithMaskLayerMixerPlayable.SetInputWeight(4, AnimTransToolTik.GetBlendTypeWeightFromTime(BlendType.EaseInOut, eyesBlinkAnimLayerWeight));
            eyesWithMaskLayerMixerPlayable.SetInputWeight(3, AnimTransToolTik.GetBlendTypeWeightFromTime(BlendType.EaseInOut, eyesCloseAnimLayerWeight));
        }

        private void ChangeAnimationClipPlayableSource(ref AnimationLayerMixerPlayable mixerPlayable, ref AnimationClipPlayable clipPlayable, AnimationClip newClip, int index)
        {
            playableGraph.DestroyPlayable(clipPlayable);
            clipPlayable = AnimationClipPlayable.Create(playableGraph, newClip);
            playableGraph.Connect(clipPlayable, 0, mixerPlayable, index);
        }

        public bool CheckExecuteBodyTransValid()
        {
            return bodyAnimTransUnit.CheckExecuteTransValid() && (!readyBodyTrans);
        }

        public bool CheckExecuteEyebrowTransValid()
        {
            return eyebrowAnimTransUnit.CheckExecuteTransValid() && (!readyEyebrowTrans);
        }
        public bool CheckExecuteEyesTransValid()
        {
            return eyesAnimTransUnit.CheckExecuteTransValid() && (!readyEyesTrans);
        }
        public bool CheckExecuteMouthTransValid()
        {
            return mouthAnimTransUnit.CheckExecuteTransValid() && (!readyMouthTrans);
        }

        public bool CheckInCloseEyes()
        {
            if (eyesCloseAnimLayerWeight > 0)
            {
                return true;
            }
            return false;
        }

        public bool CheckIsGlobalOverwriteRightHand()
        {
            if (!globalOverWriteLayerMixerPlayable.IsValid())
                return false;
            if (Mathf.Abs(globalOverWriteLayerMixerPlayable.GetInputWeight(1) - 1) < 0.001)
                return true;
            return false;
        }

        public bool CheckIsGlobalOverwriteLeftHand()
        {
            if (!globalOverWriteLayerMixerPlayable.IsValid())
                return false;
            if (Mathf.Abs(globalOverWriteLayerMixerPlayable.GetInputWeight(2) - 1) < 0.001)
                return true;
            return false;
        }

        public bool CheckBodyJustInit()
        {
            return bodyJustInit;
        }

        public string GetRightHandGlobalOverwriteAnimName()
        {
            if (globalRightHandClip != null)
                return globalRightHandClip.name;
            return "";
        }

        public string GetLeftHandGlobalOverwriteAnimName()
        {
            if (globalLeftHandClip != null)
                return globalLeftHandClip.name;
            return "";
        }

        public void SetDebugState(bool _debugMode, bool _showDebugStep, int _fullDebugStep, int _debugStep)
        {
            debugMode = _debugMode;
            showDebugStep = _showDebugStep;
            fullDebugStep = (_fullDebugStep >= 2) ? _fullDebugStep : 2;
            debugStep = Mathf.Clamp(_debugStep, 0, fullDebugStep - 1);
        }

        public void ModifyPlayableGraphStructFaceBone(bool useFaceBone)
        {
            if (useFaceBone)
            {
                playableGraph.Disconnect(faceBaseLayerMixerPlayable, 0);
                playableGraph.Connect(faceBoneLayerMixerPlayable, 0, faceBaseLayerMixerPlayable, 0);
            }
            else
            {
                playableGraph.Disconnect(faceBaseLayerMixerPlayable, 0);
                playableGraph.Connect(faceBlankAnimClipPlayable, 0, faceBaseLayerMixerPlayable, 0);
            }
        }

        public bool AnimTransSystemIsWorking()
        {
            if (playableGraph.IsValid() && bodyAnimTransUnit != null && eyebrowAnimTransUnit != null && eyesAnimTransUnit != null && mouthAnimTransUnit != null)
                return true;
            else
                return false;
        }

        private void DebugLog(AnimTransUnit animTransUnit)
        {
            Debug.Log(ZString.Concat("ABTransWeight: ", animTransUnit.animABTransWeight.ToString()));
            Debug.Log(ZString.Concat("animAStaticTransWeight: ", animTransUnit.animAStaticTransWeight.ToString()));
            Debug.Log(ZString.Concat("animBStaticTransWeight: ", animTransUnit.animBStaticTransWeight.ToString()));

            Debug.Log(ZString.Concat("InAnimA: ", animTransUnit.inAnimA.ToString()));
            Debug.Log(ZString.Concat("inAnimB: ", animTransUnit.inAnimB.ToString()));
            Debug.Log(ZString.Concat("transA2B: ", animTransUnit.transA2B.ToString()));

            Debug.Log(ZString.Concat("animASustainTime: ", animTransUnit.animASustainTime.ToString()));
            Debug.Log(ZString.Concat("animBSustainTime: ", animTransUnit.animBSustainTime.ToString()));

            Debug.Log(ZString.Concat("animAStartTime: ", animTransUnit.animAStartTime.ToString()));
            Debug.Log(ZString.Concat("animAEndTime: ", animTransUnit.animAEndTime.ToString()));
            Debug.Log(ZString.Concat("transStaticFullTime: ", animTransUnit.transStaticFullTime.ToString()));
            Debug.Log(ZString.Concat("transABFullTime: ", animTransUnit.transABFullTime.ToString()));
            Debug.Log(ZString.Concat("animAPlayRate: ", animTransUnit.animAPlayRate.ToString()));
            Debug.Log("---------------------------------------------------------------------");
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}
