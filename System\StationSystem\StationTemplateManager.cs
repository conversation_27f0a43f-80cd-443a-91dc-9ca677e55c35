using Animancer;
using Cinemachine;
using System;
using System.Collections.Generic;
using System.Linq;
using ET;
using UnityEngine;
using VGame.Framework;
using static CEditor.LookAtDataCollector;
using FIMSpace.FLook;
using FIMSpace.FEyes;
using VGame;
using uLipSync;
using Sirenix.Utilities;
using Cysharp.Text;
using System.Collections;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace CEditor
{
    [Serializable]
    [ExecuteAlways]
    public class ActorSlotInfo
    {
        public GameObject slot;
        public GameObject actor;
        public GameObject initBoneTree;
        public List<string> relativeCameraGroupList = new List<string>();
        public Dictionary<string, Transform> boneCacheMap = new Dictionary<string, Transform>();
        public List<Transform> dynamicHangings = new List<Transform>();
        public Dictionary<string, Transform> initBoneCacheMap = new Dictionary<string, Transform>();
        public List<Transform> initPoseHanging = new List<Transform>();
    }

    [Serializable]
    [ExecuteAlways]
    public class DerivativeCameraGroupInfo
    {
        public GameObject subGroupObject;
        public int slotNum;
        public int templateID;
    }

    [Serializable]
    [ExecuteAlways]
    public class CameraGroupOfTargetSlotInfo
    {
        public string targetSlotGroupTag;
        public int templateID;
        public List<int> targetSlotIndex;
        public GameObject groupGameObject;
        public GameObject spawnParentGameObject;
    }

    [ExecuteAlways]
    [Serializable]
    public class CameraInfo
    {
        public GameObject camera;
        public GameObject belongingSubGroup;
    }

    [Serializable]
    [ExecuteAlways]
    public class StationTemplateInfo
    {
        public GameObject stationTemplate;
        public int ID;
        public StationTemplateType type;
    }

    [Serializable]
    [ExecuteAlways]
    public class CloneCameraState
    {
        public GameObject cloneCamera;
        public List<GameObject> hangingGarbage;
        public List<GameObject> relevantObject;
        public CameraPerformConfig cameraPerformConfig;
        public string targetTag;
        public GameObject parentCamera;
        public bool hasChild;
        public bool justBorn;
        public bool readyToDestory;
        public bool isAbsTransformCam;
    }

    public enum HangingTargetType
    {
        Actor,
        InitPose,
        TargetGroup,
        PrefabTarget
    }

    public enum HangingPerformType
    {
        StaticReferInitPose,
        StaticReferCurrentPose,
        Dynamic
    }

    public enum SwitchStationTemplateMode
    {
        FillEmptySlot,
        MaintainActorSlotIndex
    }

    public class ActorSlotSignUpInfo
    {
        public GameObject go;
        public int slotIndex;
        public AnimTransPlayableConfig cfg;
        public LookAtSettingScrpitableObject lookAtCfg;
    }


    [ExecuteAlways]
    public class HangingBindContext
    {
        public HangingTargetType hangingTargetType;
        public int targetIndex;
    }

    [ExecuteAlways]
    public class AlignActorRotateState
    {
        public string camName;
        public string targetTag;
        public int life;
    }

    [ExecuteAlways]
    public class SingleCamChangeState
    {
        public CameraRefTransParam camRefTransParam;
        public string targetTag;
        public bool checkSimilar;
        public int life;
    }

    [ExecuteAlways]
    public class SingleCamWithAbsCamBlendState
    {
        public CameraRefTransParam camRefTransParam;
        public string targetTag;
        public Vector3 absCamWorldPos;
        public Vector3 absCamWorldRot;
        public float absCamFOV;
        public int life;
    }

    [ExecuteAlways]
    public class GroupCamChangeState
    {
        public CameraGroupRefTransParam camGroupRefTransParam;
        public string targetTag;
        public bool checkSimilar;
        public int life;
    }

    [ExecuteAlways]
    public class HiddenObjectState
    {
        public GameObject hiddenObj;
        public int life;
    }

    [ExecuteAlways]
    public class CatchInitPoseState
    {
        public int slotIndex;
        public bool loadHistroyInitPose;
        public bool releaseInitPoseToSlot;
        public int life;
    }

    [ExecuteAlways]
    public class HangingWithRelevantObjects
    {
        public Transform hangings;
        public List<GameObject> relevantObjects = new List<GameObject>();
    }
    

    [Serializable]
    [ExecuteAlways]
    public class PostprocessTemplateInfo
    {
        public int templateID;
        public GameObject templateInstance;
    }

    [Serializable]
    [ExecuteAlways]
    public class StationTemplateManager : MonoSingleton<StationTemplateManager>
    {
        //public static StationTemplateManager Instance = null;
        // 核心配置
        public Camera mainCamera;
        public CinemachineBrain brain;
        public CameraSwitchSystem cameraSwitchSystem;
        public bool useRootMotion = true;

        // 资源维护
        public StationTemplateInfo stationTemplate = new StationTemplateInfo();
        public List<ActorSlotInfo> actorSlots = new List<ActorSlotInfo>();
        public List<CameraInfo> cameras = new List<CameraInfo>();
        public List<DerivativeCameraGroupInfo> derGameraGroups = new List<DerivativeCameraGroupInfo>();
        public List<CameraGroupOfTargetSlotInfo> targetSlotCameraGroups = new List<CameraGroupOfTargetSlotInfo>();
        public List<CloneCameraState> cloneCameraBuffer = new List<CloneCameraState>();
        public PostprocessTemplateInfo postprocessTemplate;

        public List<GameObject> historysHolder { get; set; } = new List<GameObject>();

        public AlignActorRotateState alignActorRotateState = null;
        public SingleCamChangeState singleCamChangeState = null;
        public SingleCamWithAbsCamBlendState singleCamWithAbsCamBlendState = null;
        public GroupCamChangeState groupCamChangeState = null;

        //public List<HiddenObjectState> hiddenObjectBuffer = new List<HiddenObjectState>();
        public List<CatchInitPoseState> catchInitPoseStateBuffer = new List<CatchInitPoseState>();

        private bool lateCompleteClearStationTemplate = false;

        private int historyStationTemplateID = -1;
        private Vector3 historyStationTemplatePosition = Vector3.zero;
        private Quaternion historyStationTemplateRotation = Quaternion.identity;

        private Vector3 prevCutsceneEndCamPosition = Vector3.zero;
        private Quaternion prevCutsceneEndCamRotation = Quaternion.identity;
        private float prevCutsceneEndCamFOV = 30;

        void Start()
        {
            
        }

        // Update is called once per frame
        void Update()
        {
            UnityEngine.Profiling.Profiler.BeginSample("StationTemplateManager.Update");
            CleanUpHistory();
            if (lateCompleteClearStationTemplate)
                CheckAutoDestroyStationTemplate();

            if (catchInitPoseStateBuffer != null && catchInitPoseStateBuffer.Count > 0)
            {
                List<CatchInitPoseState> garbage = new List<CatchInitPoseState>();
                foreach (CatchInitPoseState state in catchInitPoseStateBuffer)
                {
                    if (state != null)
                    {
                        if (state.life <= 0)
                        {
                            CatchInitPose(state.slotIndex, state.loadHistroyInitPose);
                            if (state.releaseInitPoseToSlot)
                                ReleaseInitPose(state.slotIndex);
                            else
                                TakeBackInitPose(state.slotIndex);
                            garbage.Add(state);
                        }
                        else
                        {
                            state.life--;
                        }
                    }
                }
                foreach (CatchInitPoseState state in garbage)
                {
                    catchInitPoseStateBuffer.Remove(state);
                }
                garbage.Clear();
            }
#if UNITY_EDITOR

            ShowSlotUI();
#endif
            UnityEngine.Profiling.Profiler.EndSample();
        }
        private void FixedUpdate()
        {
            
        }

        private void LateUpdate()
        {
            UnityEngine.Profiling.Profiler.BeginSample("StationTemplateManager.LateUpdate");
            if (!useRootMotion)
            {
                if (alignActorRotateState != null)
                {
                    if (alignActorRotateState.life <= 0)
                    {
                        AlignDerCamAndSlot(alignActorRotateState.camName, alignActorRotateState.targetTag);
                        alignActorRotateState = null;
                    }
                    else
                    {
                        alignActorRotateState.life--;
                    }
                }
            }

            if (singleCamChangeState != null && cameraSwitchSystem != null)
            {
                if (singleCamChangeState.life <= 0)
                {
                    cameraSwitchSystem.ChangeSingleCamera(singleCamChangeState.camRefTransParam, singleCamChangeState.targetTag, singleCamChangeState.checkSimilar);
                    SyncCameraState(true);
                    singleCamChangeState = null;
                }
                else
                {
                    singleCamChangeState.life--;
                }
            }
            if (singleCamWithAbsCamBlendState != null && cameraSwitchSystem != null)
            {
                if (singleCamWithAbsCamBlendState.life <= 0)
                {
                    cameraSwitchSystem.ChangeSingleCameraWithAbsCamBefore(singleCamWithAbsCamBlendState.camRefTransParam, singleCamWithAbsCamBlendState.targetTag, singleCamWithAbsCamBlendState.absCamWorldPos, singleCamWithAbsCamBlendState.absCamWorldRot, singleCamWithAbsCamBlendState.absCamFOV);
                    SyncCameraState(true);
                    singleCamWithAbsCamBlendState = null;
                }
                else
                {
                    singleCamWithAbsCamBlendState.life--;
                }
            }
            if (groupCamChangeState != null)
            {
                if (groupCamChangeState.life <= 0)
                {
                    cameraSwitchSystem.ChangeBlendListCamera(groupCamChangeState.camGroupRefTransParam, groupCamChangeState.targetTag, groupCamChangeState.checkSimilar);
                    SyncCameraState(true);
                    groupCamChangeState = null;
                }
                else
                {
                    groupCamChangeState.life--;
                }
            }
            //if (hiddenObjectBuffer != null && hiddenObjectBuffer.Count > 0)
            //{
            //    List<HiddenObjectState> garbage = new List<HiddenObjectState>();
            //    foreach (HiddenObjectState state in hiddenObjectBuffer)
            //    {
            //        if (state != null)
            //        {
            //            if (state.life <= 0)
            //            {
            //                state.hiddenObj.GetComponent<SkinnedMeshRenderer>().enabled = true;
            //                garbage.Add(state);
            //            }
            //            else
            //            {
            //                state.life--;
            //            }
            //        }
            //    }
            //    foreach (HiddenObjectState state in garbage)
            //    {
            //        hiddenObjectBuffer.Remove(state);
            //    }
            //    garbage.Clear();
            //}
            SyncCameraState();
            UnityEngine.Profiling.Profiler.EndSample();
        }
        

        protected override void Init()
        {
            base.Init();
            Debug.Log("StationTemplateManager Generated");
            if (cameraSwitchSystem == null)
            {
                if (this.gameObject.GetComponent<CameraSwitchSystem>() == null)
                    cameraSwitchSystem = this.gameObject.AddComponent<CameraSwitchSystem>();
                else
                    cameraSwitchSystem = this.gameObject.GetComponent<CameraSwitchSystem>();
            }
            CompleteClearErrorState();
        }


        private void OnDestroy()
        {
            DestroyHistorysHolder();
            DestroyCameraBrain();
            CompletelyClear();

            base.OnDestroy();
        }

        private void InitCameraBrain()
        {
            if (brain != null)
                DestroyCameraBrain();

            var brainHolder = new GameObject();
            brainHolder.name = StationTemplateDataCollector.namingConvention[NamingConventionTypes.CameraBrain];
            brainHolder.transform.parent = null;

            brain = brainHolder.AddComponent<CinemachineBrain>();

            brain.m_UpdateMethod = CinemachineBrain.UpdateMethod.LateUpdate;
            brain.m_BlendUpdateMethod = CinemachineBrain.BrainUpdateMethod.LateUpdate;
            brain.ManualUpdate();
        }

        private void DestroyCameraBrain()
        {
            if (brain != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(brain.gameObject);
#else
                Destroy(brain.gameObject);
#endif
            }
        }

        private void DestroyHistorysHolder()
        {
            if (historysHolder != null && historysHolder.Count > 0)
            {
                for (int i = 0; i < historysHolder.Count; i++)
                {
#if UNITY_EDITOR
                    DestroyImmediate(historysHolder[i]);
#else
                    Destroy(historysHolder[i]);
#endif
                }
            }
        }

        private void CompleteClearErrorState()
        {
            GameObject history = GameObject.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.StationTemplateHistory]);
            while (history != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(history);
#else
                Destroy(history);
#endif
                history = GameObject.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.StationTemplateHistory]);
            }

            GameObject cameraBrain = GameObject.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CameraBrain]);
            while (cameraBrain != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(cameraBrain);
#else
                Destroy(cameraBrain);
#endif
                cameraBrain = GameObject.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CameraBrain]);
            }
        }

        private void ClearUnrelevantVirtualCameras()
        {
            var virtualCamList = GameObject.FindObjectsOfType<CinemachineVirtualCameraBase>();
            foreach (CinemachineVirtualCameraBase virtualCam in virtualCamList)
                virtualCam.gameObject.SetActive(false);
        }

        private void InitMainCamParam()
        {
            if (mainCamera != null)
            {
                var tempBrain = mainCamera.gameObject.GetComponent<CinemachineBrain>();
                if (tempBrain != null)
                {
#if UNITY_EDITOR
                    DestroyImmediate(tempBrain);
#else
                    Destroy(tempBrain);
#endif
                }

                SyncCameraState(true);

                //CinemachineBrain CB = mainCamera.gameObject.GetComponent<CinemachineBrain>();
                //if (CB == null)
                //    CB = mainCamera.gameObject.AddComponent<CinemachineBrain>();

                //CB.m_UpdateMethod = CinemachineBrain.UpdateMethod.LateUpdate;
                //CB.m_BlendUpdateMethod = CinemachineBrain.BrainUpdateMethod.LateUpdate;

                //CB.ManualUpdate();
            }
        }

        private void ReleaseCameraBrainAbnormalState()
        {
            Debug.Log("ReleaseCameraBrainAbnormalState");
            if (brain == null) return;
            var vc = brain.ActiveVirtualCamera;
            if (vc == null) return;
            GameObject cam = vc.VirtualCameraGameObject;
            Debug.Log(ZString.Concat("ReleaseCameraBrainAbnormalState ", cam.name));
            if (cloneCameraBuffer != null  && cloneCameraBuffer.Where(x => ReferenceEquals(x.cloneCamera, cam)).Count() == 0)
            {
                ClearUnrelevantVirtualCameras();
                InitCameraBrain();
            }
        }

        public bool IsPostprocessTemplateRegistered(int templateID)
        {
            // 要求缓存 ID 一致且实例合法
            return postprocessTemplate != null && postprocessTemplate.templateID == templateID && postprocessTemplate.templateInstance != null;
        }

        public void ReusePostprocessTemplate()
        {
            if (postprocessTemplate == null || postprocessTemplate.templateInstance == null)
                return;

            // 重新启用缓存实例
            ActivatePostprocessTemplate(postprocessTemplate.templateInstance);
        }

        public void RegisterPostprocessTemplate(int templateID, GameObject templateInstance)
        {
            if (templateInstance == null)
                return;

            // 缓存非空时
            if (postprocessTemplate != null && postprocessTemplate.templateInstance != null)
            {
                // ID 相同时直接启用实例
                if (postprocessTemplate.templateID == templateID)
                {
                    ActivatePostprocessTemplate(postprocessTemplate.templateInstance);
                    return;
                }

                // 销毁实例
#if UNITY_EDITOR
                DestroyImmediate(postprocessTemplate.templateInstance);
#else
                Destroy(postprocessTemplate.templateInstance);
#endif
                postprocessTemplate = null;
            }

            // 重新注册
            postprocessTemplate = new PostprocessTemplateInfo
            {
                templateID = templateID,
                templateInstance = templateInstance,
            };
            ActivatePostprocessTemplate(templateInstance);
        }

        private void ActivatePostprocessTemplate(GameObject templateInstance)
        {
            if (templateInstance == null)
                return;

            templateInstance.name = "Postprocess Template";
            templateInstance.transform.SetParent(transform, false);
            templateInstance.transform.position = Vector3.zero;
            templateInstance.transform.rotation = Quaternion.identity;
            templateInstance.transform.localScale = Vector3.one;
            templateInstance.Active(true);
        }

        public void UnregisterPostprocessTemplate(int templateID)
        {
            if (postprocessTemplate == null)
                return;

            // 缓存实例非法时直接置空缓存
            var cacheInstance = postprocessTemplate.templateInstance;
            if (cacheInstance == null)
            {
                postprocessTemplate = null;
                return;
            }

            // 暂时隐藏，而非销毁，避免下一次需要复用
            if (postprocessTemplate.templateID == templateID)
            {
                cacheInstance.Active(false);
                return;
            }

            // 其他情况下直接销毁
#if UNITY_EDITOR
            DestroyImmediate(cacheInstance);
#else
            Destroy(cacheInstance);
#endif
            postprocessTemplate = null;
        }

        // 站位模板对外接口系列
        // 万能接口
        public void SignUpStationTemplate(GameObject templateObject, int templateID, StationTemplateType type)
        {
            if (templateObject == null) return;
            if ((stationTemplate == null || stationTemplate.stationTemplate == null) && transform.childCount == 0)
            {
                GenerateNewStationTemplate(templateObject, templateID, type);
            }
            else
            {
                SwitchStationTemplate(templateObject, templateID, type, SwitchStationTemplateMode.FillEmptySlot);
            }
        }

        public void SignUpStationTemplate(GameObject templateObject, int templateID, StationTemplateType type, SwitchStationTemplateMode switchMode)
        {
            if (templateObject == null) return;
            if ((stationTemplate == null || stationTemplate.stationTemplate == null) && transform.childCount == 0)
            {
                GenerateNewStationTemplate(templateObject, templateID, type);
            }
            else
            {
                SwitchStationTemplate(templateObject, templateID, type, switchMode);
            }
        }

        private void SwitchStationTemplate(GameObject newTemplateObject, int templateID, StationTemplateType type, SwitchStationTemplateMode switchMode)
        {
            if (newTemplateObject == null) return;

            List<GameObject> oldActors = new List<GameObject>();
            for (int i = 0; i < actorSlots.Count; i++)
            {
                oldActors.Add(CleanUpActorInSlotSoft(i, false, false, false, false, false, false, false));
            }
            CleanUpRuntimeDataRelevantToStationTemplate();

            GenerateNewStationTemplate(newTemplateObject, templateID, type);
            ReloadActorSlotsAndObject(oldActors, switchMode);
        }


        // 角色槽对外接口系列
        // 万能接口
        public GameObject SwitchActor(int slotIndex, GameObject newActor, AnimTransPlayableConfig animTransPlayableConfig, LookAtSettingScrpitableObject lookAtConfig/*, bool useIndependentActorToCatchInitPose*/, bool prepareTransA2C)
        {
            if (slotIndex >= 0 && slotIndex < actorSlots.Count)
            {
                if (newActor != null)
                {
                    GameObject oldActorTemp = CleanUpActorInSlotSoft(slotIndex, true, false, false, false, false, false, false);
                    Vector3 actorOriWorldPosition;
                    Quaternion actorOriWorldRotation;
                    RegisterActor(slotIndex, newActor, false, out actorOriWorldPosition, out actorOriWorldRotation);
                    InitActorComponent(slotIndex, animTransPlayableConfig, lookAtConfig, actorOriWorldPosition, actorOriWorldRotation);
                    if (prepareTransA2C)
                        InitTransA2CState(slotIndex);

                    catchInitPoseStateBuffer.Add(new CatchInitPoseState()
                    {
                        slotIndex = slotIndex,
                        loadHistroyInitPose = true,
                        releaseInitPoseToSlot = true,
                        life = 0
                    });
                    return oldActorTemp;
                }
                else
                {
                    return CleanUpActorInSlotSoft(slotIndex, true, true, true, true, true, true, true);
                }
            }
            else
            {
                Debug.Log("slotIndex Out Of Station Template slots Range");
            }
            return null;
        }


        // 多镜头组分析逻辑
        private List<CameraGroupOfTargetSlotInfo> AnalysisRelevantCameraGroup(int slotIndex)
        {
            List<CameraGroupOfTargetSlotInfo> result = new List<CameraGroupOfTargetSlotInfo>();

            if (targetSlotCameraGroups == null)
                return result;

            foreach (CameraGroupOfTargetSlotInfo targetSlotCameraGroupInfo in targetSlotCameraGroups)
            {
                if(targetSlotCameraGroupInfo.targetSlotIndex != null && targetSlotCameraGroupInfo.targetSlotIndex.Count > 0)
                {
                    bool hasIndex = false;
                    foreach (int i in targetSlotCameraGroupInfo.targetSlotIndex)
                        if (i == slotIndex)
                            hasIndex = true;
                    if (hasIndex)
                        result.Add(targetSlotCameraGroupInfo);
                }
            }
            return result;
        }

        private async void RegisterDerivativeCameraGroupsV2(int templateID, int slotCount)
        {
            List<List<int>> derGroupSlotTarget = StationTemplateToolsKit.GetAllSubset(slotCount);
            List<List<int>> garbage = new List<List<int>>();
            foreach (List<int> ts in derGroupSlotTarget)
            {
                if (targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == StationTemplateToolsKit.GetTargetTag(ts)) != null)
                    garbage.Add(ts);
            }
            foreach (var g in garbage)
                derGroupSlotTarget.Remove(g);

            if (derGroupSlotTarget.Count > 0)
            {
                var baseCfg = CfgManager.tables.TbCEditorStationTemplate.Get(templateID);
                if (baseCfg != null)
                {
                    string derCamGroupTag = baseCfg.DerTags;
                    if (derCamGroupTag != null)
                    {
                        // 注册默认衍生镜头
                        List<DerivativeCameraGroupInfo> defaultDerCamsInfo = StationTemplateToolsKit.GetDefaultDerCamGroupIDs(derCamGroupTag);
                        if (defaultDerCamsInfo != null)
                        {
                            foreach (var d in defaultDerCamsInfo)
                            {
                                LoadDerCameraGroup(d.templateID);
                            }
                        }
                        // 注册虚拟衍生镜头组
                        foreach (List<int> derGroup in derGroupSlotTarget)
                        {
                            DerivativeCameraGroupInfo derCameraGroup = derGameraGroups.Find(x => x.slotNum == derGroup.Count);
                            string targetSlotTag = StationTemplateToolsKit.GetTargetTag(derGroup);
                            targetSlotCameraGroups.Add(new CameraGroupOfTargetSlotInfo()
                            {
                                targetSlotGroupTag = targetSlotTag,
                                targetSlotIndex = derGroup,
                                templateID = derCameraGroup == null ? -1 : derCameraGroup.templateID,
                                groupGameObject = null,
                                spawnParentGameObject = null
                            });
                        }
                        // 注册覆写镜头组
                        List<CameraGroupOfTargetSlotInfo> overwriteCamGroupsInfo = StationTemplateToolsKit.GetOverwriteDerCamGroupInfo(derCamGroupTag);
                        if (overwriteCamGroupsInfo != null)
                        {
                            foreach (var o in overwriteCamGroupsInfo)
                            {
                                CameraGroupOfTargetSlotInfo defaultInfo = targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == o.targetSlotGroupTag);
                                if (defaultInfo != null)
                                    defaultInfo.templateID = o.templateID;
                            }
                        }
                        // 加载覆写镜头镜头组并完善targetSlotCameraGroups相关信息
                        foreach (var t in targetSlotCameraGroups)
                        {
                            if (t == null) continue;
                            if (t.templateID == templateID) continue;
                            if (t.templateID != -1)
                            {
                                DerivativeCameraGroupInfo derCamInfo = derGameraGroups.Find(x => x.templateID == t.templateID);
                                if (derCamInfo == null)
                                {
                                    derCamInfo = await LoadDerCameraGroup(t.templateID);
                                }
                                if (derCamInfo != null)
                                {
                                    t.groupGameObject = derCamInfo.subGroupObject;
                                    t.spawnParentGameObject = CalculateDerCamGroupSpawnPoint(derCamInfo, t.targetSlotIndex, t.targetSlotGroupTag);
                                }
                            }
                        }
                        // 清理无镜头组targetTag
                        List<CameraGroupOfTargetSlotInfo> targetTagGarbage = new List<CameraGroupOfTargetSlotInfo>();
                        foreach (var t in targetSlotCameraGroups)
                            if (t.templateID == -1)
                                targetTagGarbage.Add(t);
                        foreach (var g in targetTagGarbage)
                            targetSlotCameraGroups.Remove(g);
                    }
                }
            }
        }

        private async ETTask<DerivativeCameraGroupInfo> LoadDerCameraGroup(int derStationTemplateID)
        {
            DerivativeCameraGroupInfo hasDerCamGroup = derGameraGroups.Find(x => x.templateID == derStationTemplateID);
            if (hasDerCamGroup != null)
                return hasDerCamGroup;

            var derCfg = CfgManager.tables.TbCEditorStationTemplate.Get(derStationTemplateID);
            if (derCfg != null && !string.IsNullOrEmpty(derCfg.FileName))
            {
                var stationTemplatePrefab = await DialogueUtil.Load<GameObject>(derCfg.FileName);
                if (stationTemplatePrefab != null)
                {
                    GameObject templateObject = Instantiate(stationTemplatePrefab);
                    if (templateObject != null)
                    {
                        Transform camGroup = templateObject.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CameraGroup]);
                        Transform actorGroup = templateObject.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.ActorGroup]);
                        DerivativeCameraGroupInfo derInfo = null;
                        if (camGroup != null && camGroup.childCount > 0 && actorGroup != null && actorGroup.childCount > 0)
                        {
                            Transform subCamGroup = camGroup.GetChild(0);
                            subCamGroup.parent = getCameraGroup().transform;
                            subCamGroup.localPosition = Vector3.zero;
                            subCamGroup.localRotation = Quaternion.identity;
                            subCamGroup.localScale = Vector3.one;

                            actorGroup.parent = subCamGroup;
                            actorGroup.localPosition = Vector3.zero;
                            actorGroup.localRotation = Quaternion.identity;
                            actorGroup.localScale = Vector3.one;
                            actorGroup.name = StationTemplateDataCollector.namingConvention[NamingConventionTypes.DerCamOriSlotGroup];

                            derInfo = new DerivativeCameraGroupInfo()
                            {
                                subGroupObject = subCamGroup.gameObject,
                                slotNum = StationTemplateToolsKit.GetSlotCountByStationTemplateName(derCfg.Type),
                                templateID = derStationTemplateID
                            };
                            derGameraGroups.Add(derInfo);
                        }
#if UNITY_EDITOR
                        DestroyImmediate(templateObject);
#else
                        Destroy(templateObject);
#endif
                        return derInfo;
                    }
                }
            }
            return null;
        }

        private CameraInfo AnalysisAndObtainCameraInfo(string cameraName, string cameraTag)
        {
            CameraGroupOfTargetSlotInfo subgroupInfo = null;
            if (targetSlotCameraGroups != null)
            {
                subgroupInfo = targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == cameraTag);
            }
            if (cameras != null && subgroupInfo != null)
            {

                CameraInfo cameraInfo = cameras.Find(x => ((x.camera.name == cameraName) && ReferenceEquals(x.belongingSubGroup, subgroupInfo.groupGameObject)));
                return cameraInfo;
            }
            Debug.Log("cameras is null");
            return null;
        }

        private CameraInfo AnalysisAndObtainDerivativeCameraInfo(string baseCameraLensID, string cameraTag)
        {
            CameraGroupOfTargetSlotInfo subgroupInfo = null;
            if (targetSlotCameraGroups != null)
            {
                subgroupInfo = targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == cameraTag);
            }
            if (cameras != null && subgroupInfo != null)
            {

                CameraInfo cameraInfo = cameras.Find(x => ((CCameraTools.GetCameraLensID(x.camera.name) == baseCameraLensID) && ReferenceEquals(x.belongingSubGroup, subgroupInfo.groupGameObject)));
                return cameraInfo;
            }
            Debug.Log("cameras is null");
            return null;
        }

        private bool CalculateCamAutoPitchOffset(string cameraTag, out float offset)
        {
            if (string.IsNullOrEmpty(cameraTag) || cameraTag.Length != 1) 
            {
                offset = 0f;
                return false;
            } 
            int index = StationTemplateToolsKit.GetSlotIndexByStringName(cameraTag);
            if (index <0 || index >= actorSlots.Count || actorSlots[index].actor == null)
            {
                offset = 0f;
                return false;
            }
            string name = actorSlots[index].actor.name;
            if (name.ToLower().Contains("NaLuLu".ToLower()) ||
                name.ToLower().Contains("ShanBao".ToLower()) ||
                name.ToLower().Contains("TaFei".ToLower()))
            {
                offset = 0.15f;
                return true;
            }
            offset = 0f;
            return false;
        }

        private CameraRefTransParam TransCameraIDAndTargetTagToCameraRefParam(CameraIDTransParam camParam, string targetTag, bool useAutoPitchDown)
        {
            useAutoPitchDown = false;
            if (camParam == null)
                return null;
            var cfg = CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(camParam.camID);
            if (cfg != null)
            {
                string camName = cfg.UnityName;
                if (!string.IsNullOrEmpty(camName))
                {
                    CameraNameInfo cni = CCameraTools.AnalysisCameraInfoFromName(camName);
                    float autoPitchOffset;
                    bool autoPitchOffsetValid = CalculateCamAutoPitchOffset(targetTag, out autoPitchOffset);
                    if (cni == null)
                    {
                        Log.LogError("CamName:{0} not valid", camName);
                        return null;
                    }
                    if (cni.camType == CameraType.SingleDerivative)
                    {
                        CameraInfo camInfo = AnalysisAndObtainDerivativeCameraInfo(cni.derivativeCamInfo.baseCamLensID, targetTag);
                        if (camInfo != null && camInfo.camera != null && camInfo.camera.GetComponent<CinemachineVirtualCamera>() != null)
                        {
                            CameraRefTransParam cameraTransParam = new CameraRefTransParam();
                            cameraTransParam.cam = camInfo.camera.GetComponent<CinemachineVirtualCamera>();
                            cameraTransParam.cameraPerformConfig = camParam.cameraPerformConfig;
                            cameraTransParam.cameraPerformConfig.cameraControllerData.screenRotateOffset += cni.derivativeCamInfo.dutch;
                            cameraTransParam.cameraPerformConfig.cameraControllerData.screenHorizontalOffset += cni.derivativeCamInfo.screenXOffset;
                            cameraTransParam.cameraPerformConfig.cameraControllerData.pitchOffset += cni.derivativeCamInfo.followYOffset;
                            if (useAutoPitchDown && autoPitchOffsetValid)
                                cameraTransParam.cameraPerformConfig.cameraControllerData.pitchOffset = autoPitchOffset;
                            return cameraTransParam;
                        }
                    }
                    else
                    {
                        CameraInfo camInfo = AnalysisAndObtainCameraInfo(camName, targetTag);
                        if (camInfo != null && camInfo.camera != null && camInfo.camera.GetComponent<CinemachineVirtualCamera>() != null)
                        {
                            CameraRefTransParam cameraTransParam = new CameraRefTransParam();
                            cameraTransParam.cam = camInfo.camera.GetComponent<CinemachineVirtualCamera>();
                            cameraTransParam.cameraPerformConfig = camParam.cameraPerformConfig;
                            if (useAutoPitchDown && autoPitchOffsetValid)
                                cameraTransParam.cameraPerformConfig.cameraControllerData.pitchOffset = autoPitchOffset;
                            return cameraTransParam;
                        }
                    }
                }
            }
            return null;
        }

        private bool CheckCameraTargetValid(string targetGroupTag)
        {
            for (int i = 0; i < targetGroupTag.Length; i++)
            {
                int index = StationTemplateToolsKit.GetSlotIndexByStringName(targetGroupTag[i].ToString());
                if (index < 0 || index >= actorSlots.Count)
                    return false;
                if (actorSlots[index].actor == null)
                    return false;
            }
            return true;
            
        }

        // 处理镜头对角色朝向的影响
        private void RotateSlotByOffsetName(string rotateOffsetName, CameraGroupOfTargetSlotInfo cameraGroupInfo, Transform oriCamera)
        {
            if (cameraGroupInfo == null || string.IsNullOrEmpty(rotateOffsetName) || oriCamera == null)
                return;

            if (!StationTemplateToolsKit.NeedToRotateSlot(rotateOffsetName))
            {
                if (oriCamera.parent != null)
                {
                    string[] subGroupNameTags = oriCamera.parent.name.Split("#");
                    rotateOffsetName = subGroupNameTags[subGroupNameTags.Length - 1];
                }
            }
            if (!StationTemplateToolsKit.NeedToRotateSlot(rotateOffsetName))
                return;

            List<SlotRotateOffsetInfo> slotROffsets = StationTemplateToolsKit.AnalysisSlotsRotateOffsets(rotateOffsetName);
            if (slotROffsets != null && cameraGroupInfo.targetSlotIndex != null && slotROffsets.Count == cameraGroupInfo.targetSlotIndex.Count)
            {
                if (cameraGroupInfo.spawnParentGameObject == getCameraGroup())
                {
                    foreach (SlotRotateOffsetInfo i in slotROffsets)
                    {
                        actorSlots[cameraGroupInfo.targetSlotIndex[i.slotIndex]].slot.transform.localRotation = Quaternion.Euler(new Vector3(0, i.rotateYoffset, 0));
                    }
                }
                else
                {
                    Transform oriActorGroup = cameraGroupInfo.groupGameObject.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.DerCamOriSlotGroup]);
                    if (oriActorGroup != null && oriActorGroup.childCount == slotROffsets.Count)
                    {
                        foreach (SlotRotateOffsetInfo i in slotROffsets)
                        {
                            oriActorGroup.GetChild(i.slotIndex).localRotation = Quaternion.Euler(new Vector3(0, i.rotateYoffset, 0));
                            actorSlots[cameraGroupInfo.targetSlotIndex[i.slotIndex]].slot.transform.rotation = oriActorGroup.GetChild(i.slotIndex).rotation;
                        }
                    }
                }
            }
        }

        public void AlignDerCamAndSlot(string cameraName, string slotTargetTag)
        {
            if (!string.IsNullOrEmpty(cameraName) && !string.IsNullOrEmpty(slotTargetTag))
            {
                CameraNameInfo cni = CCameraTools.AnalysisCameraInfoFromName(cameraName);
                CameraGroupOfTargetSlotInfo subgroupInfo = targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == slotTargetTag);
                if (cni != null && subgroupInfo != null)
                {
                    subgroupInfo.groupGameObject.transform.parent = subgroupInfo.spawnParentGameObject.transform;
                    subgroupInfo.groupGameObject.transform.localPosition = Vector3.zero;
                    subgroupInfo.groupGameObject.transform.localRotation = Quaternion.identity;
                    subgroupInfo.groupGameObject.transform.localScale = Vector3.one;

                    CameraInfo cameraInfo = cameras.Find(x => ((x.camera.name == cameraName) && ReferenceEquals(x.belongingSubGroup, subgroupInfo.groupGameObject)));

                    RotateSlotByOffsetName(cni.baseCamInfo.slotOffset, subgroupInfo, cameraInfo.camera.transform);
                }
            }
        }

        private GameObject CalculateDerCamGroupSpawnPoint(DerivativeCameraGroupInfo derCamGroup, List<int> targetSlotIndex, string spawnTargetName)
        {
            if (derCamGroup == null || derCamGroup.subGroupObject == null || targetSlotIndex == null || targetSlotIndex.Count == 0 || string.IsNullOrEmpty(spawnTargetName))
                return null;
            if (derCamGroup.slotNum != targetSlotIndex.Count)
                return null;

            GameObject derCamSpwnTarget = new GameObject();
            derCamSpwnTarget.transform.parent = getDerCamPawnGroup().transform;
            derCamSpwnTarget.name = CCameraTools.GetUniqueGameObjectName(spawnTargetName, derCamSpwnTarget.transform.parent);

            derCamGroup.subGroupObject.transform.localPosition = Vector3.zero;
            derCamGroup.subGroupObject.transform.localRotation = Quaternion.identity;
            derCamGroup.subGroupObject.transform.localScale = Vector3.one;

            if (actorSlots != null)
            {
                if (derCamGroup.slotNum == 1)
                {
                    if (targetSlotIndex[0] >= 0 && targetSlotIndex[0] < actorSlots.Count)
                    {
                        if (actorSlots[targetSlotIndex[0]] != null && actorSlots[targetSlotIndex[0]].slot != null)
                        {
                            derCamSpwnTarget.transform.position = actorSlots[targetSlotIndex[0]].slot.transform.position;
                            derCamSpwnTarget.transform.rotation = actorSlots[targetSlotIndex[0]].slot.transform.rotation;

                            return derCamSpwnTarget;
                        }
                    }
                }
                else
                {
                    Transform oriActorSlotGroup = derCamGroup.subGroupObject.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.DerCamOriSlotGroup]);
                    if (oriActorSlotGroup != null && oriActorSlotGroup.childCount > 0 && targetSlotIndex[0] >= 0 && targetSlotIndex[0] < actorSlots.Count)
                    {
                        Vector3 derFirstSlot = oriActorSlotGroup.GetChild(0).position;
                        Vector3 derEndSlot = oriActorSlotGroup.GetChild(oriActorSlotGroup.childCount - 1).position;

                        Vector3 targetFirstSlot = actorSlots[targetSlotIndex[0]].slot.transform.position;
                        Vector3 targetEndSlot = actorSlots[targetSlotIndex[targetSlotIndex.Count - 1]].slot.transform.position;

                        float RYOffset = StationTemplateToolsKit.CalculateAlign1To2RotateYOffset(derFirstSlot, derEndSlot, targetFirstSlot, targetEndSlot);
                        derCamGroup.subGroupObject.transform.rotation = Quaternion.Euler(0, RYOffset, 0);

                        oriActorSlotGroup = derCamGroup.subGroupObject.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.DerCamOriSlotGroup]);
                        Vector3 derFirstSlotAfterRotate = oriActorSlotGroup.GetChild(0).position;

                        derCamGroup.subGroupObject.transform.position += StationTemplateToolsKit.CalculateAlign1To2PositionOffset(derFirstSlotAfterRotate, targetFirstSlot);

                        derCamSpwnTarget.transform.position = derCamGroup.subGroupObject.transform.position;
                        derCamSpwnTarget.transform.rotation = derCamGroup.subGroupObject.transform.rotation;

                        return derCamSpwnTarget;
                    }
                }
            }
            return null;
        }


        // 切换站位模板相关逻辑
        private void ReloadActorSlotsAndObject(List<GameObject> oldActors, SwitchStationTemplateMode switchMode)
        {
            if (oldActors == null)
                return;

            switch (switchMode)
            {
                case SwitchStationTemplateMode.MaintainActorSlotIndex:
                    for(int i = 0; i < actorSlots.Count; i++)
                    {
                        if (i < oldActors.Count && oldActors[i] != null)
                        {
                            AnimTransPlayableSystem animSystem = oldActors[i].GetComponent<AnimTransPlayableSystem>();
                            LookAtManager lookAtSystem = oldActors[i].GetComponent<LookAtManager>();
                            AnimTransPlayableConfig animConfig = null;
                            LookAtSettingScrpitableObject lookAtConfig = null;
                            if (animSystem != null)
                                animConfig = animSystem.config;
                            if (lookAtSystem != null)
                                lookAtConfig = lookAtSystem.lookAtSetting;

                            if (animConfig != null)
                            {
                                SwitchActor(i, oldActors[i], animConfig, lookAtConfig, false);
                            }
                        }
                    }
                    break;
                case SwitchStationTemplateMode.FillEmptySlot:
                    int loadSlotIndex = 0;
                    for (int i = 0; i < oldActors.Count; i++)
                    {
                        if (oldActors[i] != null)
                        {
                            AnimTransPlayableSystem animSystem = oldActors[i].GetComponent<AnimTransPlayableSystem>();
                            LookAtManager lookAtSystem = oldActors[i].GetComponent<LookAtManager>();
                            AnimTransPlayableConfig animConfig = null;
                            LookAtSettingScrpitableObject lookAtConfig = null;
                            if (animSystem != null)
                                animConfig = animSystem.config;
                            if (lookAtSystem != null)
                                lookAtConfig = lookAtSystem.lookAtSetting;

                            if (animConfig != null)
                            {
                                SwitchActor(loadSlotIndex, oldActors[i], animConfig, lookAtConfig, false);
                            }
                            
                            loadSlotIndex++;
                        }
                    }
                    break;
                default: break;
            }
        }

        private void HoldCameraStateBeforClear()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null /*&& historyHolder == null*/)
            {
                GameObject cloneCams = getCloneCameraGroup();
                GameObject hangings = getHangingGroup();
                GameObject targetGroups = getTargetGroup();
                if (cloneCams != null && cloneCams.transform.childCount > 0)
                {
                    GameObject history = new GameObject(StationTemplateDataCollector.namingConvention[NamingConventionTypes.StationTemplateHistory]);
                    if (cloneCameraBuffer != null)
                    {
                        foreach (CloneCameraState clone in cloneCameraBuffer)
                        {
                            if (clone != null)
                            {
                                if (clone.hangingGarbage != null && clone.relevantObject != null)
                                {
                                    List<GameObject> mergeList = new List<GameObject>(clone.relevantObject);
                                    mergeList.AddRange(clone.hangingGarbage.Except(clone.relevantObject));
                                    clone.hangingGarbage.Clear();
                                    clone.hangingGarbage = mergeList;

                                    foreach (GameObject hanging in clone.hangingGarbage)
                                    {
                                        if (hanging != null)
                                        {
                                            hanging.transform.parent = hangings.transform;
                                        }
                                    }
                                }
                            }
                        }
                    }
                        
                    cloneCams.transform.parent = history.transform;
                    if (hangings != null)
                        hangings.transform.parent = history.transform;
                    if (targetGroups != null)
                        targetGroups.transform.parent = history.transform;
                    historysHolder.Add(history);
                }
            }
        }

        private void CleanUpHistory()
        {
            if (historysHolder != null && historysHolder.Count > 0)
            {
                List<GameObject> garbage = new List<GameObject>();
                for (int i = 0;  i < historysHolder.Count; i++)
                {
                    if (historysHolder[i] == null)
                    {
                        garbage.Add(historysHolder[i]);
                        continue;
                    }
                    Transform cloneCam = historysHolder[i].transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CloneCameraGroup]);
                    if ((cloneCam != null && cloneCam.childCount == 0) || cloneCam == null)
                    {
                        garbage.Add(historysHolder[i]);
                    }
                }
                for (int i = 0;i < garbage.Count; i++)
                {
                    historysHolder.Remove(garbage[i]);
#if UNITY_EDITOR
                    DestroyImmediate(garbage[i]);
#else
                    Destroy(garbage[i]);
#endif
                    garbage.Clear();
                }
            }
        }

        private void CleanUpRuntimeDataRelevantToStationTemplate()
        {
            if (actorSlots != null)
                actorSlots.Clear();
            if (derGameraGroups != null)
                derGameraGroups.Clear();
            if (targetSlotCameraGroups != null)
                targetSlotCameraGroups.Clear();
            if (cameras != null)
                cameras.Clear();
            //if (cloneCameraBuffer != null)
            //    cloneCameraBuffer.Clear();
#if UNITY_EDITOR
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
                DestroyImmediate(stationTemplate.stationTemplate);
#else
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
                Destroy(stationTemplate.stationTemplate);
#endif
            stationTemplate = null;
        }

        private void CleanUpActorInSlotHard(int slotIndex)
        {
            if (actorSlots == null)
                return;
            if (slotIndex >= 0 && slotIndex < actorSlots.Count)
            {
                if (actorSlots[slotIndex].dynamicHangings != null)
                    actorSlots[slotIndex].dynamicHangings.Clear();
                if (actorSlots[slotIndex].boneCacheMap != null)
                    actorSlots[slotIndex].boneCacheMap.Clear();
                if (actorSlots[slotIndex].relativeCameraGroupList != null)
                    actorSlots[slotIndex].relativeCameraGroupList.Clear();
                if (actorSlots[slotIndex].initBoneCacheMap != null)
                    actorSlots[slotIndex].initBoneCacheMap.Clear();
                if (actorSlots[slotIndex].initPoseHanging != null)
                    actorSlots[slotIndex].initPoseHanging.Clear();
                //actorSlots[slotIndex].staticHangings.Clear();

#if UNITY_EDITOR
                if (actorSlots[slotIndex].initBoneTree != null)
                    DestroyImmediate(actorSlots[slotIndex].initBoneTree);
                if (slotIndex >= 0 && slotIndex <= actorSlots.Count)
                    if (actorSlots[slotIndex].slot != null)
                        for (int i = 0; i < actorSlots[slotIndex].slot.transform.childCount; i++)
                            DestroyImmediate(actorSlots[slotIndex].slot.transform.GetChild(i).gameObject);
#else
                if (actorSlots[slotIndex].initBoneTree != null)
                    Destroy(actorSlots[slotIndex].initBoneTree);
                if (slotIndex >= 0 && slotIndex <= actorSlots.Count)
                    if (actorSlots[slotIndex].slot != null)
                        for (int i = 0; i < actorSlots[slotIndex].slot.transform.childCount; i++)
                            Destroy(actorSlots[slotIndex].slot.transform.GetChild(i).gameObject);
#endif
            }
        }

        private GameObject CleanUpActorInSlotSoft(int slotIndex, bool maintainCamPerfom, bool destroyAnimSystem, bool destroyInitPose, bool destroyLookAtSystem, bool destroyBlinkSystem, bool destroyHandheldSyetem, bool destroyLipSystem)
        {
            if (actorSlots == null)
                return null;
            if (slotIndex >= 0 && slotIndex < actorSlots.Count)
            {
                if (actorSlots[slotIndex].boneCacheMap != null)
                    actorSlots[slotIndex].boneCacheMap.Clear();
                if (actorSlots[slotIndex].relativeCameraGroupList != null)
                    actorSlots[slotIndex].relativeCameraGroupList.Clear();
                if (actorSlots[slotIndex].initBoneCacheMap != null)
                    actorSlots[slotIndex].initBoneCacheMap.Clear();

                Transform staticHangingTransform = getHangingGroup().transform;

                if (slotIndex >= 0 && slotIndex <= actorSlots.Count)
                {
                    if (actorSlots[slotIndex].dynamicHangings != null)
                    {
                        for (int i = 0; i < actorSlots[slotIndex].dynamicHangings.Count; i++)
                        {
                            if (actorSlots[slotIndex].dynamicHangings[i] != null)
                            {
                                if (maintainCamPerfom)
                                {
                                    if (actorSlots[slotIndex].dynamicHangings[i].gameObject != null)
                                    {
                                        if (CheckHangingIsUsing(actorSlots[slotIndex].dynamicHangings[i].gameObject))
                                        {
                                            actorSlots[slotIndex].dynamicHangings[i].parent = staticHangingTransform;
                                            continue;
                                        }
                                    }
                                }
#if UNITY_EDITOR
                                DestroyImmediate(actorSlots[slotIndex].dynamicHangings[i].gameObject);
#else
                                Destroy(actorSlots[slotIndex].dynamicHangings[i].gameObject);
#endif
                            }
                        }
                    }
                    if (actorSlots[slotIndex].initPoseHanging != null)
                    {
                        for (int i = 0; i < actorSlots[slotIndex].initPoseHanging.Count; i++)
                        {
                            if (actorSlots[slotIndex].initPoseHanging[i] != null)
                            {
                                if (maintainCamPerfom)
                                {
                                    if (actorSlots[slotIndex].initPoseHanging[i].gameObject != null)
                                    {
                                        if (CheckHangingIsUsing(actorSlots[slotIndex].initPoseHanging[i].gameObject))
                                        {
                                            actorSlots[slotIndex].initPoseHanging[i].parent = staticHangingTransform;
                                            continue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (destroyInitPose)
                {
                    if (actorSlots[slotIndex].initBoneTree != null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(actorSlots[slotIndex].initBoneTree);
#else
                        Destroy(actorSlots[slotIndex].initBoneTree);
#endif
                    }
                }
                else
                {
                    if (actorSlots[slotIndex].initBoneTree != null)
                    {
                        TakeBackInitPose(slotIndex);
                    }
                }

                if (destroyLookAtSystem)
                {
                    if (actorSlots[slotIndex].actor != null)
                    {
                        if (actorSlots[slotIndex].actor.GetComponent<FLookAnimator>() != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<FLookAnimator>());
#else
                            Destroy(actorSlots[slotIndex].actor.GetComponent<FLookAnimator>());
#endif
                        }

                        if (actorSlots[slotIndex].actor.GetComponent<FEyesAnimator>() != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<FEyesAnimator>());
#else
                            Destroy(actorSlots[slotIndex].actor.GetComponent<FEyesAnimator>());
#endif
                        }

                        if (actorSlots[slotIndex].actor.GetComponent<LookAtManager>() != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<LookAtManager>());
#else
                            Destroy(actorSlots[slotIndex].actor.GetComponent<LookAtManager>());
#endif
                        }
                    }
                    
                }

                if (destroyBlinkSystem)
                {
                    if (actorSlots[slotIndex].actor != null && actorSlots[slotIndex].actor.GetComponent<EyeBlinkManager>() != null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<EyeBlinkManager>());
#else
                        Destroy(actorSlots[slotIndex].actor.GetComponent<EyeBlinkManager>());
#endif
                    }
                }

                if (destroyHandheldSyetem)
                {
                    if (actorSlots[slotIndex].actor != null && actorSlots[slotIndex].actor.GetComponent<HandheldManage>() != null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<HandheldManage>());
#else
                        Destroy(actorSlots[slotIndex].actor.GetComponent<HandheldManage>());
#endif
                    }
                }

                if (destroyHandheldSyetem)
                {
                    if (actorSlots[slotIndex].actor != null && actorSlots[slotIndex].actor.GetComponent<ClothManager>() != null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<ClothManager>());
#else
                        Destroy(actorSlots[slotIndex].actor.GetComponent<ClothManager>());
#endif
                    }
                }
                
                if (destroyLipSystem)
                {
                    if (actorSlots[slotIndex].actor != null)
                    {
                        if (actorSlots[slotIndex].actor.GetComponent<LipSyncManager>() != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<LipSyncManager>());
#else
                            Destroy(actorSlots[slotIndex].actor.GetComponent<LipSyncManager>());
#endif
                        }

                        if (actorSlots[slotIndex].actor.GetComponent<uLipSyncBlendShape>() != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<uLipSyncBlendShape>());
#else
                            Destroy(actorSlots[slotIndex].actor.GetComponent<uLipSyncBlendShape>());
#endif
                        }

                        if (actorSlots[slotIndex].actor.GetComponent<uLipSyncBakedDataPlayer>() != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(actorSlots[slotIndex].actor.GetComponent<uLipSyncBakedDataPlayer>());
#else
                            Destroy(actorSlots[slotIndex].actor.GetComponent<uLipSyncBakedDataPlayer>());
#endif
                        }
                    }

                }

                if (actorSlots[slotIndex].dynamicHangings != null)
                    actorSlots[slotIndex].dynamicHangings.Clear();
                if (actorSlots[slotIndex].initPoseHanging != null)
                    actorSlots[slotIndex].initPoseHanging.Clear();

                GameObject temp = null;
                if (actorSlots[slotIndex].actor != null)
                {
                    actorSlots[slotIndex].actor.transform.parent = null;
                    temp = actorSlots[slotIndex].actor;
                    actorSlots[slotIndex].actor = null;

                    if (destroyAnimSystem)
                    {
                        AnimTransPlayableSystem animSystem = temp.GetComponent<AnimTransPlayableSystem>();
                        if (animSystem != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(animSystem);
#else
                            Destroy(animSystem);
#endif
                        }
                        AnimTransMaster animMaster = temp.GetComponent<AnimTransMaster>();
                        if (animMaster != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(animMaster);
#else
                            Destroy(animMaster);
#endif
                        }
                    }
                    else
                    {
                        AnimTransMaster animMaster = temp.GetComponent<AnimTransMaster>();
                        if (animMaster != null)
                            animMaster.TriggerRecordRootmotion(temp.transform.position, temp.transform.rotation);
                    }


                    if (actorSlots[slotIndex].slot != null)
                    {
                        for (int i = 0; i < actorSlots[slotIndex].slot.transform.childCount; i++)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(actorSlots[slotIndex].slot.transform.GetChild(i).gameObject);
#else
                            Destroy(actorSlots[slotIndex].slot.transform.GetChild(i).gameObject);
#endif
                        }
                    }
                    return temp;
                }
            }
            return null;
        }

        private GameObject CleanUpActorComponent(GameObject actor)
        {
            if (actor == null)
                return null;

            if (actor.GetComponent<FLookAnimator>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<FLookAnimator>());
#else
                Destroy(actor.GetComponent<FLookAnimator>());
#endif
            }

            if (actor.GetComponent<FEyesAnimator>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<FEyesAnimator>());
#else
                Destroy(actor.GetComponent<FEyesAnimator>());
#endif
            }

            if (actor.GetComponent<LookAtManager>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<LookAtManager>());
#else
                Destroy(actor.GetComponent<LookAtManager>());
#endif
            }

            if (actor.GetComponent<EyeBlinkManager>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<EyeBlinkManager>());
#else
                Destroy(actor.GetComponent<EyeBlinkManager>());
#endif
            }

            if (actor.GetComponent<HandheldManage>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<HandheldManage>());
#else
                Destroy(actor.GetComponent<HandheldManage>());
#endif
            }

            if (actor.GetComponent<LipSyncManager>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<LipSyncManager>());
#else
                Destroy(actor.GetComponent<LipSyncManager>());
#endif
            }

            if (actor.GetComponent<uLipSyncBlendShape>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<uLipSyncBlendShape>());
#else
                Destroy(actor.GetComponent<uLipSyncBlendShape>());
#endif
            }

            if (actor.GetComponent<uLipSyncBakedDataPlayer>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<uLipSyncBakedDataPlayer>());
#else
                Destroy(actor.GetComponent<uLipSyncBakedDataPlayer>());
#endif
            }

            if (actor.GetComponent<AnimTransPlayableSystem>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<AnimTransPlayableSystem>());
#else
                Destroy(actor.GetComponent<AnimTransPlayableSystem>());
#endif
            }

            if (actor.GetComponent<AnimTransMaster>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<AnimTransMaster>());
#else
                Destroy(actor.GetComponent<AnimTransMaster>());
#endif
            }

            if (actor.GetComponent<AnimancerComponent>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<AnimancerComponent>());
#else
                Destroy(actor.GetComponent<AnimancerComponent>());
#endif
            }

            return actor;
        }

        private GameObject SetupInitPose(GameObject actor, AnimTransPlayableConfig config, StationTemplatePostureType postureType)
        {
            if (actor == null)
                return null;

            Animator animator = actor.GetComponent<Animator>();
            if (animator == null)
                animator = actor.AddComponent<Animator>();
            if (animator != null)
            {
                animator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
                animator.applyRootMotion = false;
                animator.updateMode = AnimatorUpdateMode.Normal;
            }

            if (actor.GetComponent<AnimancerComponent>() != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(actor.GetComponent<AnimancerComponent>());
#else
                Destroy(actor.GetComponent<AnimancerComponent>());
#endif
            }

            SetupInitPoseAnimSystem initSystem = actor.GetComponent<SetupInitPoseAnimSystem>();
            if (initSystem == null)
                initSystem = actor.AddComponent<SetupInitPoseAnimSystem>();

            switch (postureType)
            {
                case StationTemplatePostureType.Stand:
                    initSystem.clip = config.defaultStandIdleClip;
                    break;
                case StationTemplatePostureType.Sit:
                    initSystem.clip = config.defaultSitIdleClip; 
                    break;
                case StationTemplatePostureType.Null:
                    initSystem.clip = config.defaultStandIdleClip;
                    break;
                default:
                    initSystem.clip = config.defaultStandIdleClip;
                    break;
            }
            initSystem.UpdateAnim();
            return actor;
        }

        private bool CheckHangingIsUsing(GameObject targetHanging)
        {
            if ( targetHanging != null && cloneCameraBuffer != null)
            {
                foreach(CloneCameraState cloneCam in cloneCameraBuffer)
                {
                    if(cloneCam != null)
                    {
                        CinemachineVirtualCamera cv = cloneCam.cloneCamera.GetComponent<CinemachineVirtualCamera>();
                        if (cv != null)
                        {
                            if (cv.Follow != null)
                            {
                                if (cv.Follow.GetComponent<CinemachineTargetGroup>() != null)
                                {
                                    foreach (CinemachineTargetGroup.Target t in cv.Follow.GetComponent<CinemachineTargetGroup>().m_Targets)
                                        if (t.target.parent != null)
                                            if (t.target.parent.parent != null)
                                                if (t.target.parent.parent.gameObject == targetHanging)
                                                    return true;
                                }
                                else
                                {
                                    if (cv.Follow.parent != null)
                                        if (cv.Follow.parent.parent != null)
                                            if (cv.Follow.parent.parent.gameObject == targetHanging)
                                                return true;
                                }
                            }

                            if (cv.LookAt != null)
                            {
                                if (cv.LookAt.GetComponent<CinemachineTargetGroup>() != null)
                                {
                                    foreach (CinemachineTargetGroup.Target t in cv.LookAt.GetComponent<CinemachineTargetGroup>().m_Targets)
                                        if (t.target.parent != null)
                                            if (t.target.parent.parent != null)
                                                if (t.target.parent.parent.gameObject == targetHanging)
                                                    return true;
                                }
                                else
                                {
                                    if (cv.LookAt.parent != null)
                                        if (cv.LookAt.parent.parent != null)
                                            if (cv.LookAt.parent.parent.gameObject == targetHanging)
                                                return true;
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }

        private void SyncStaticHangingFromDynamicForSlotActor(int slotIndex)
        {
            GameObject staticHangingRoot = getHangingGroup();
            if (staticHangingRoot == null) 
                return;
            for (int i = 0; i < staticHangingRoot.transform.childCount; i++)
            {
                HangingBindContext context = CCameraTools.GetHangingBindContextByHangingName(staticHangingRoot.transform.GetChild(i).name);
                if (context != null)
                {
                    if (context.hangingTargetType == HangingTargetType.Actor)
                    {
                        if (context.targetIndex >= 0 && context.targetIndex < actorSlots.Count)
                        {
                            Transform dynamicHanging = actorSlots[slotIndex].dynamicHangings.Find(x => x.name == CCameraTools.GetHangingStructuredName(staticHangingRoot.transform.GetChild(i).name, true));

                            if (dynamicHanging != null)
                            {
                                staticHangingRoot.transform.GetChild(i).transform.position = dynamicHanging.position;
                                staticHangingRoot.transform.GetChild(i).transform.rotation = dynamicHanging.rotation;
                                staticHangingRoot.transform.GetChild(i).transform.localScale = Vector3.one;
                            }
                        }
                    }
                }
            }
        }

        public void SyncStaticHangingFromDynamicForSlotActorGameObject(GameObject actor)
        {
            int slotIndex = -1;
            if (actorSlots != null && actor != null)
            {
                for (int i = 0; i < actorSlots.Count; i++)
                {
                    if (actorSlots[i].actor != null && ReferenceEquals(actorSlots[i].actor, actor))
                    {
                        slotIndex = i; break;
                    }
                }
            }
            if (slotIndex >= 0)
            {
                SyncStaticHangingFromDynamicForSlotActor(slotIndex);
            }
        }


        // 站位模板生成逻辑
        private void GenerateNewStationTemplate(GameObject templateObject, int templateID, StationTemplateType type)
        {
            if (templateObject == null)
                return;
            if (RegisterStationTemplate(templateObject, templateID, type))
            {
                Transform actorGroup = getActorGroup().transform;
                Transform camGroup = getCameraGroup().transform;
                CollectTemplateActorSlot(actorGroup);
                SignUpRelevantCameraGroup();
                RegisterDerivativeCameraGroupsV2(templateID, actorSlots.Count);
                CollectAndStandardizingTemplateCamera(camGroup);
                BindCameraSystemScript(camGroup);
            }
        }

        private bool RegisterStationTemplate(GameObject newStationTemplate, int templateID, StationTemplateType type)
        {
            if (newStationTemplate != null)
            {
                newStationTemplate.transform.parent = transform;
                newStationTemplate.transform.localPosition = new Vector3(0, 0, 0);
                newStationTemplate.transform.localRotation = Quaternion.identity;
                newStationTemplate.transform.localScale = Vector3.one;

                if (!newStationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.HangingGroup]))
                {
                    GameObject hangingGroup = new GameObject(StationTemplateDataCollector.namingConvention[NamingConventionTypes.HangingGroup]);
                    hangingGroup.transform.parent = newStationTemplate.transform;
                    hangingGroup.transform.localPosition = new Vector3(0f, 0f, 0f);
                    hangingGroup.transform.localRotation = Quaternion.identity;
                    hangingGroup.transform.localScale = Vector3.one;
                }
                if (!newStationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CloneCameraGroup]))
                {
                    GameObject runtimeCamGroup = new GameObject(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CloneCameraGroup]);
                    runtimeCamGroup.transform.parent = newStationTemplate.transform;
                    runtimeCamGroup.transform.localPosition = new Vector3(0f, 0f, 0f);
                    runtimeCamGroup.transform.localRotation = Quaternion.identity;
                    runtimeCamGroup.transform.localScale = Vector3.one;
                }
                if (!newStationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.TargetGroup]))
                {
                    GameObject targetGroup = new GameObject(StationTemplateDataCollector.namingConvention[NamingConventionTypes.TargetGroup]);
                    targetGroup.transform.parent = newStationTemplate.transform;
                    targetGroup.transform.localPosition = new Vector3(0f, 0f, 0f);
                    targetGroup.transform.localRotation = Quaternion.identity;
                    targetGroup.transform.localScale = Vector3.one;
                }
                if (!newStationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.DerCamPawnGroup]))
                {
                    GameObject derCamPawnGroup = new GameObject(StationTemplateDataCollector.namingConvention[NamingConventionTypes.DerCamPawnGroup]);
                    derCamPawnGroup.transform.parent = newStationTemplate.transform;
                    derCamPawnGroup.transform.localPosition = new Vector3(0f, 0f, 0f);
                    derCamPawnGroup.transform.localRotation = Quaternion.identity;
                    derCamPawnGroup.transform.localScale = Vector3.one;
                }
                stationTemplate = new StationTemplateInfo
                {
                    stationTemplate = newStationTemplate,
                    ID = templateID,
                    type = type
                };

                GameObject camGroupObj = getCameraGroup();
                Transform cameraGroup;
                if (camGroupObj != null)
                    cameraGroup = camGroupObj.transform;
                else
                    return false;

                GameObject actorGroupObj = getActorGroup();
                int slotCount;
                if (actorGroupObj != null)
                    slotCount = actorGroupObj.transform.childCount;
                else
                    return false;
                if (slotCount != StationTemplateToolsKit.GetSlotCountByStationTemplateType(type))
                    return false;
                if (slotCount <= 0) return false;

                List<int> fullIndexList = new List<int>();
                for(int i = 0; i < slotCount; i++)
                    fullIndexList.Add(i);
                string defaultTargetSlotGroupTag = StationTemplateToolsKit.GetTargetTag(fullIndexList);
                for (int i = 0; i < cameraGroup.childCount; i++)
                {
                    if (cameraGroup.GetChild(i).name == StationTemplateDataCollector.namingConvention[NamingConventionTypes.FixFollowPoint])
                        continue;
                    targetSlotCameraGroups.Add(new CameraGroupOfTargetSlotInfo()
                    {
                        targetSlotGroupTag = defaultTargetSlotGroupTag,
                        targetSlotIndex = fullIndexList,
                        templateID = templateID,
                        groupGameObject = cameraGroup.GetChild(i).gameObject,
                        spawnParentGameObject = cameraGroup.gameObject
                    });
                }
                return true;
            }
            return false;
        }

        private void CollectTemplateActorSlot(Transform actorGroup)
        {
            actorSlots.SetLength(actorGroup.childCount);
            for (int i = 0; i < actorGroup.childCount; i++)
            {
                ActorSlotInfo slotInfo = new ActorSlotInfo
                {
                    slot = actorGroup.GetChild(i).gameObject,
                    actor = null,
                    relativeCameraGroupList = new List<string>(),
                    boneCacheMap = new Dictionary<string, Transform>(),
                    dynamicHangings = new List<Transform>()
                };
                int index = StationTemplateToolsKit.GetSlotIndexByStringName(actorGroup.GetChild(i).name);
                if (index >= 0)
                    actorSlots[index] = slotInfo;
            }
        }

        private void SignUpRelevantCameraGroup()
        {
            for(int i = 0; i < actorSlots.Count; i++)
            {
                actorSlots[i].relativeCameraGroupList = actorSlots[i].relativeCameraGroupList = AnalysisRelevantCameraGroup(i).Select(x => x.targetSlotGroupTag).ToList();
            }
        }

        private void CollectAndStandardizingTemplateCamera(Transform cameraGroup)
        {
            for (int i = 0; i < cameraGroup.childCount; i++)
            {
                for (int j = 0; j < cameraGroup.GetChild(i).childCount; j++)
                {
                    GameObject go = cameraGroup.GetChild(i).GetChild(j).gameObject;
                    if (CCameraTools.IsCamera(go.name))
                    {
                        if (CCameraTools.IsCloneCamera(go.name))
                        {
#if UNITY_EDITOR
                            DestroyImmediate(cameraGroup.GetChild(i).GetChild(j).gameObject);
#else
                            Destroy(cameraGroup.GetChild(i).GetChild(j).gameObject);
#endif
                            continue;
                        }
                        if (go.GetComponent<CinemachineVirtualCamera>())
                            go.GetComponent<CinemachineVirtualCamera>().Priority = 0;
                        else
                            continue;
                        if (go.GetComponents<CameraController>().Length == 0)
                            go.AddComponent<CameraController>();
                        go.SetActive(false);
                        CameraInfo cameraInfo = new CameraInfo
                        {
                            camera = go,
                            belongingSubGroup = cameraGroup.GetChild(i).gameObject
                        };
                        cameras.Add(cameraInfo);
                    }
                }
            }
        }

        private void BindCameraSystemScript(Transform cameraGroup)
        {
            //if (cameraGroup.gameObject.GetComponents<CameraManager>().Length == 0)
            //{
            //    cameraGroup.gameObject.AddComponent<CameraManager>();
            //}
            
        }


        // 角色注册
        private bool RegisterActor(int slotIndex, GameObject actor, bool instantiate, out Vector3 actorOriWorldPosition, out Quaternion actorOriWorldRotation)
        {
            if (actorSlots != null)
            {
                if (slotIndex >= 0 && slotIndex < actorSlots.Count)
                {
                    if (actorSlots[slotIndex].slot != null && actor != null)
                    {
                        CleanUpActorInSlotHard(slotIndex);
                        if (instantiate)
                        {
                            GameObject actorObject = Instantiate(actor, actorSlots[slotIndex].slot.transform);
                            actorSlots[slotIndex].actor = actorObject;
                            actorOriWorldPosition = actorObject.transform.position;
                            actorOriWorldRotation = actorObject.transform.rotation;
                        }
                        else
                        {
                            actorSlots[slotIndex].actor = actor;
                            actorOriWorldPosition = actor.transform.position;
                            actorOriWorldRotation = actor.transform.rotation;
                            actor.transform.parent = actorSlots[slotIndex].slot.transform;
                            actor.transform.localPosition = new Vector3(0f, 0f, 0f);
                            actor.transform.localRotation = Quaternion.identity;
                            actor.transform.localScale = Vector3.one;
                        }
                        actorSlots[slotIndex].relativeCameraGroupList = AnalysisRelevantCameraGroup(slotIndex).Select(x => x.targetSlotGroupTag).ToList();
                        actorSlots[slotIndex].dynamicHangings = new List<Transform>();
                        actorSlots[slotIndex].boneCacheMap = new Dictionary<string, Transform>();
                        actorSlots[slotIndex].initBoneCacheMap = new Dictionary<string, Transform>();
                        actorSlots[slotIndex].initPoseHanging = new List<Transform>();

                        //StandardFaceMeshPath(actor.transform);
                        return true;
                    }
                }
            }
            actorOriWorldPosition = Vector3.zero;
            actorOriWorldRotation = Quaternion.identity;
            return false;
        }

        // 角色初始化
        private void InitActorComponent(int slotIndex, AnimTransPlayableConfig playableParamConfig, LookAtSettingScrpitableObject lookAtParamConfig, Vector3 oriActorWorldPosition, Quaternion oriActorWorldRotation)
        {
            if (actorSlots == null || slotIndex < 0 || slotIndex >= actorSlots.Count)
                return;

            GameObject actorObject = actorSlots[slotIndex].actor;
            if (actorObject == null)
                return;
            // 关闭rootMotion
            if (actorObject.GetComponent<Animator>() != null){
                actorObject.GetComponent<Animator>().applyRootMotion = useRootMotion;
            }
            else{
                actorObject.AddComponent<Animator>().applyRootMotion = useRootMotion;
            }
            // 关闭animancer
            //if (actorObject.GetComponent<AnimancerComponent>() != null)
            //    actorObject.GetComponent<AnimancerComponent>().enabled = false;

            // 挂载AnimTransPlayableSystem
            AnimTransPlayableSystem tempAnimTransPlayableSystem = actorObject.GetComponent<AnimTransPlayableSystem>();
            if (tempAnimTransPlayableSystem == null)
                tempAnimTransPlayableSystem = actorObject.AddComponent<AnimTransPlayableSystem>();

            if (playableParamConfig != null)
                tempAnimTransPlayableSystem.config = playableParamConfig;
            
            // 挂载 animTransMaster
            AnimTransMaster tempMaster = actorObject.GetComponent<AnimTransMaster>();
            if (tempMaster == null)
            {
                tempMaster = actorObject.AddComponent<AnimTransMaster>();
                tempMaster.currentMode = MasterMode.AnimTrans;
                tempMaster.useAutoTransTime = false;
                tempMaster.useAutoMirror = false;
            }
            if (tempMaster.animTransPlayableSystem == null || !tempMaster.animTransPlayableSystem.AnimTransSystemIsWorking())
                tempMaster.InitTransSystem();

            //if (tempMaster != null && stationTemplate != null)
            //    tempMaster.TriggerCutsceneChange(actorSlots[slotIndex].slot.transform.position, actorSlots[slotIndex].slot.transform.rotation, oriActorWorldPosition, oriActorWorldRotation, stationTemplate.ID);
            StartCoroutine(TriggerCutsceneChangeOnTheEnd(tempMaster, slotIndex, oriActorWorldPosition, oriActorWorldRotation));

            // 挂载LookAtManager
            LookAtManager lookAtComponent = actorObject.GetComponent<LookAtManager>();
            if (lookAtComponent == null || (lookAtComponent != null && (lookAtComponent.fLookAnimator == null || lookAtComponent.fEyeAnimator == null)))
            {
                lookAtComponent = actorObject.AddComponent<LookAtManager>();
                if (lookAtComponent != null)
                {
                    if (lookAtParamConfig != null)
                        lookAtComponent.lookAtSetting = lookAtParamConfig;
                    else
                    {
                        LookAtSettingScrpitableObject config = ScriptableObject.CreateInstance<LookAtSettingScrpitableObject>();
                        lookAtComponent.lookAtSetting = config;
                    }
                    lookAtComponent.Init();
                }
            }
            // 挂载LipSyncManager
            LipSyncManager lipSyncManager = actorObject.GetComponent<LipSyncManager>();
            if (!lipSyncManager)
            {
                lipSyncManager = actorObject.AddComponent<LipSyncManager>();
                lipSyncManager.Init();
            }
            
            // 挂载BlinkManager
            EyeBlinkManager blinkComponent = actorObject.GetComponent<EyeBlinkManager>();
            if (blinkComponent == null)
            {
                blinkComponent = actorObject.AddComponent<EyeBlinkManager>();
                if (tempMaster != null)
                    blinkComponent.animMaster = tempMaster;
            }

            // 挂载HandHoldManager
            HandheldManage handheldManage = actorObject.GetComponent<HandheldManage>();
            if (handheldManage == null)
            {
                handheldManage = actorObject.AddComponent<HandheldManage>();
                if (tempMaster != null)
                    handheldManage.animMaster = tempMaster;
            }
            
            // 挂载ClothManager
            ClothManager clothManager = actorObject.GetComponent<ClothManager>();
            if (clothManager == null)
            {
                clothManager = actorObject.AddComponent<ClothManager>();
                if (tempMaster != null)
                    clothManager.Init();
            }
        }

        IEnumerator TriggerCutsceneChangeOnTheEnd(AnimTransMaster master, int slotIndex, Vector3 oriActorWorldPosition, Quaternion oriActorWorldRotation)
        {
            yield return new WaitForEndOfFrame();
            if (master != null && stationTemplate != null)
                master.TriggerCutsceneChange(actorSlots[slotIndex].slot.transform.position, actorSlots[slotIndex].slot.transform.rotation, oriActorWorldPosition, oriActorWorldRotation, stationTemplate.ID);
        }


        public static void InitActorComponent(GameObject actorObject, AnimTransPlayableConfig playableParamConfig)
        {
            if (actorObject == null)
                return;
            // 关闭rootMotion
            if (actorObject.GetComponent<Animator>() != null)
                actorObject.GetComponent<Animator>().applyRootMotion = true;

            // 挂载AnimTransPlayableSystem
            AnimTransPlayableSystem tempAnimTransPlayableSystem = actorObject.GetComponent<AnimTransPlayableSystem>();
            if (tempAnimTransPlayableSystem == null)
            {
                tempAnimTransPlayableSystem = actorObject.AddComponent<AnimTransPlayableSystem>();
            }
            if (playableParamConfig != null)
                tempAnimTransPlayableSystem.config = playableParamConfig;
            
            // 挂载 animTransMaster
            AnimTransMaster tempMaster = actorObject.GetComponent<AnimTransMaster>();
            if (tempMaster == null)
            {
                tempMaster = actorObject.AddComponent<AnimTransMaster>();
                tempMaster.currentMode = MasterMode.AnimTrans;
                tempMaster.useAutoTransTime = false;
                tempMaster.useAutoMirror = false;
            }
            if (tempMaster.animTransPlayableSystem == null || !tempMaster.animTransPlayableSystem.AnimTransSystemIsWorking())
                tempMaster.InitTransSystem();

            tempMaster.applyRootmotionControl = false;

            // 挂载LipSyncManager
            LipSyncManager lipSyncManager = actorObject.GetComponent<LipSyncManager>();
            if (!lipSyncManager)
            {
                lipSyncManager = actorObject.AddComponent<LipSyncManager>();
                lipSyncManager.Init();
            }
            
            // 挂载BlinkManager
            EyeBlinkManager blinkComponent = actorObject.GetComponent<EyeBlinkManager>();
            if (blinkComponent == null)
            {
                blinkComponent = actorObject.AddComponent<EyeBlinkManager>();
                if (tempMaster != null)
                    blinkComponent.animMaster = tempMaster;
            }

            // 挂载HandHoldManager
            HandheldManage handheldManage = actorObject.GetComponent<HandheldManage>();
            if (handheldManage == null)
            {
                handheldManage = actorObject.AddComponent<HandheldManage>();
                if (tempMaster != null)
                    handheldManage.animMaster = tempMaster;
            }
            
            // 挂载ClothManager
            ClothManager clothManager = actorObject.GetComponent<ClothManager>();
            if (clothManager == null)
            {
                clothManager = actorObject.AddComponent<ClothManager>();
                if (tempMaster != null)
                    clothManager.Init();
            }
        }
            
        private void InitTransA2CState(int slotIndex)
        {
            if (actorSlots != null && slotIndex >= 0 && slotIndex < actorSlots.Count && actorSlots[slotIndex].actor != null)
            {
                AnimTransMaster master = actorSlots[slotIndex].actor.GetComponent<AnimTransMaster>();
                if (master != null)
                    master.TransToCPerformance();
            }
        }

        private void CatchInitPose(int slotIndex, bool loadHistroyInitPose)
        {
            if (stationTemplate != null && actorSlots != null && slotIndex >= 0 && slotIndex < actorSlots.Count && actorSlots[slotIndex].actor != null)
            {
                StationTemplatePostureType targetPoseType = StationTemplateToolsKit.GetActorPostureByStationTemplateType(stationTemplate.type);

                switch (targetPoseType)
                {
                    case StationTemplatePostureType.Stand:
                        Transform histroySitInitBoneTree = actorSlots[slotIndex].actor.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit]);
                        if (histroySitInitBoneTree == null)
                            histroySitInitBoneTree = actorSlots[slotIndex].slot.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit]);
                        if (histroySitInitBoneTree != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(histroySitInitBoneTree.gameObject);
#else
                            Destroy(histroySitInitBoneTree.gameObject);
#endif
                        }
                        break;
                    case StationTemplatePostureType.Sit:
                        Transform histroyStandInitBoneTree = actorSlots[slotIndex].actor.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand]);
                        if (histroyStandInitBoneTree == null)
                            histroyStandInitBoneTree = actorSlots[slotIndex].slot.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand]);
                        if (histroyStandInitBoneTree != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(histroyStandInitBoneTree.gameObject);
#else
                            Destroy(histroyStandInitBoneTree.gameObject);
#endif
                        }
                        break;
                    default:
                        Transform histroyStandInitBoneTreeDefault = actorSlots[slotIndex].actor.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand]);
                        Transform histroySitInitBoneTreeDefault = actorSlots[slotIndex].actor.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit]);
                        if (histroyStandInitBoneTreeDefault == null)
                            histroyStandInitBoneTreeDefault = actorSlots[slotIndex].slot.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand]);
                        if (histroySitInitBoneTreeDefault)
                            histroySitInitBoneTreeDefault = actorSlots[slotIndex].slot.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit]);
                        if (histroyStandInitBoneTreeDefault != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(histroyStandInitBoneTreeDefault.gameObject);
#else
                            Destroy(histroyStandInitBoneTreeDefault.gameObject);
#endif
                        }
                        if (histroySitInitBoneTreeDefault != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(histroySitInitBoneTreeDefault.gameObject);
#else
                            Destroy(histroySitInitBoneTreeDefault.gameObject);
#endif
                        }
                        break;
                }

                Transform histroyInitBoneTree = null;
                switch (targetPoseType)
                {
                    case StationTemplatePostureType.Stand:
                        histroyInitBoneTree = actorSlots[slotIndex].actor.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand]);
                        if (histroyInitBoneTree == null)
                            histroyInitBoneTree = actorSlots[slotIndex].slot.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand]);
                        break;
                    case StationTemplatePostureType.Sit:
                        histroyInitBoneTree = actorSlots[slotIndex].actor.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit]);
                        if (histroyInitBoneTree == null)
                            histroyInitBoneTree = actorSlots[slotIndex].slot.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit]);
                        break;
                    default:
                        break;
                }
                if (histroyInitBoneTree != null)
                {
                    if (loadHistroyInitPose)
                    {
                        actorSlots[slotIndex].initBoneTree = histroyInitBoneTree.gameObject;
                        return;
                    }
                    else
                    {
#if UNITY_EDITOR
                        DestroyImmediate(histroyInitBoneTree.gameObject);
#else
                        Destroy(histroyInitBoneTree.gameObject);
#endif
                    }
                }

                GameObject boneTree = null;
                AnimTransMaster animMaster = actorSlots[slotIndex].actor.GetComponent<AnimTransMaster>();
                if (animMaster != null)
                {
                    boneTree = animMaster.CatchPose(targetPoseType, null, 0, CatchInitPoseTree);
                    if (boneTree != null)
                        actorSlots[slotIndex].initBoneTree = boneTree;
                }

                //if (catchIndependentInitPose && independentInitPoseActor != null)
                //{
                //    boneTree = independentInitPoseActor.transform.Find("Root");
                //    if (boneTree != null)
                //        actorSlots[slotIndex].initBoneTree = Instantiate(boneTree.gameObject);
                //}
                //else
                //{
                //    boneTree = actorSlots[slotIndex].actor.transform.Find("Root");
                //    if (boneTree != null)
                //        actorSlots[slotIndex].initBoneTree = Instantiate(boneTree.gameObject);
                //}
                
                if (actorSlots[slotIndex].initBoneTree != null)
                {
                    switch (targetPoseType)
                    {
                        case StationTemplatePostureType.Stand:
                            actorSlots[slotIndex].initBoneTree.name = StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand];
                            break;
                        case StationTemplatePostureType.Sit:
                            actorSlots[slotIndex].initBoneTree.name = StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit];
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        public GameObject CatchInitPoseTree(GameObject actor)
        {
            var root = actor.transform.Find("Root");
            GameObject initPose = null;
            if (root != null)
            {
                initPose = Instantiate(root.gameObject);
                var held = initPose.GetComponentsInChildren<HandheldGripper>();
                foreach (var h in held)
                {
                    if (h == null) continue;
#if UNITY_EDITOR
                    DestroyImmediate(h.gameObject.transform.parent.gameObject);
#else
                    Destroy(h.gameObject);
#endif
                }
                var componentList = initPose.GetComponentsInChildren<MonoBehaviour>();
                foreach (var comp in componentList)
                {
                    if (comp == null) continue;
#if UNITY_EDITOR
                    DestroyImmediate(comp);
#else
                    Destroy(comp);
#endif
                }
            }
            return initPose;
        }

        private void ReleaseInitPose(int slotIndex)
        {
            if (actorSlots != null && actorSlots.Count > slotIndex)
            {
                if (actorSlots[slotIndex].initBoneTree != null && actorSlots[slotIndex].actor != null)
                {
                    actorSlots[slotIndex].initBoneTree.transform.parent = actorSlots[slotIndex].slot.transform;
                    actorSlots[slotIndex].initBoneTree.transform.localPosition = Vector3.zero;
                    actorSlots[slotIndex].initBoneTree.transform.localRotation = Quaternion.identity;
                    actorSlots[slotIndex].initBoneTree.transform.localScale = Vector3.one;
                }
            }
        }

        private void TakeBackInitPose(int slotIndex)
        {
            if (actorSlots != null && actorSlots.Count > slotIndex)
            {
                if (actorSlots[slotIndex].initBoneTree != null && actorSlots[slotIndex].actor != null)
                {
                    actorSlots[slotIndex].initBoneTree.transform.parent = actorSlots[slotIndex].actor.transform;
                    actorSlots[slotIndex].initBoneTree.transform.localPosition = Vector3.zero;
                    actorSlots[slotIndex].initBoneTree.transform.localRotation = Quaternion.identity;
                    actorSlots[slotIndex].initBoneTree.transform.localScale = Vector3.one;
                }
            }
        }

        //private void HiddenActor(int slotIndex, int hideFrameNum)
        //{
        //    if (actorSlots != null && slotIndex >= 0 && slotIndex < actorSlots.Count && actorSlots[slotIndex].actor != null)
        //    {
        //        Transform displayMesh = actorSlots[slotIndex].actor.transform.Find("Mesh_Lod0");
        //        if (displayMesh != null)
        //        {
        //            for (int i = 0; i < displayMesh.childCount; i++)
        //            {
        //                if (displayMesh.GetChild(i).gameObject.activeSelf)
        //                {
        //                    SkinnedMeshRenderer render = displayMesh.GetChild(i).gameObject.GetComponent<SkinnedMeshRenderer>();
        //                    if (render != null)
        //                    {
        //                        render.enabled = false;
        //                        if (hideFrameNum < 0)
        //                            continue;
        //                        hiddenObjectBuffer.Add(new HiddenObjectState()
        //                        {
        //                            hiddenObj = displayMesh.GetChild(i).gameObject,
        //                            life = hideFrameNum
        //                        });
        //                    }
        //                }
        //            }
        //        }
        //    }
        //}

        private HangingWithRelevantObjects GetDynamicHangingOnActor(HangingInfoStringOffsetByName hangingInfo, int slotIndex)
        {
            if (actorSlots[slotIndex] == null || actorSlots[slotIndex].actor == null)
                return null;

            HangingBindContext context = new HangingBindContext() { hangingTargetType = HangingTargetType.Actor, targetIndex = slotIndex };
            string targetHangingName = CCameraTools.GetHangingStructuredName(
                context,
                hangingInfo.baseObjName,
                hangingInfo.offset,
                true);
            if (!string.IsNullOrEmpty(targetHangingName))
            {
                List<Transform> hangings = actorSlots[slotIndex].dynamicHangings.Where(x => x.name == targetHangingName).ToList();
                if (hangings.Count > 0)
                    return new HangingWithRelevantObjects() 
                    {
                        hangings = hangings[0],
                        relevantObjects = new List<GameObject>() { hangings[0].gameObject }
                    };

                Transform hangingBoneRef = null;
                if (actorSlots[slotIndex].boneCacheMap.ContainsKey(hangingInfo.baseObjName))
                    hangingBoneRef = actorSlots[slotIndex].boneCacheMap[hangingInfo.baseObjName];
                if (hangingBoneRef == null)
                {
                    if (!string.IsNullOrEmpty(hangingInfo.baseObjName))
                    {
                        var hangingBoneNameMap = CfgManager.tables.TbCEditorHangingBoneName.Get(hangingInfo.baseObjName);
                        if (hangingBoneNameMap != null)
                        {
                            string boneName = hangingBoneNameMap.BoneName;
                            //string boneName = StationTemplateDataCollector.boneNameTrans[hangingInfo.baseObjName];
                            hangingBoneRef = FindChildInTransform(actorSlots[slotIndex].actor.transform, boneName, new List<string>() { StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand], StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit] });
                            if (hangingBoneRef != null)
                                actorSlots[slotIndex].boneCacheMap.Add(hangingInfo.baseObjName, hangingBoneRef);
                        }
                    }
                }
                if (hangingBoneRef != null)
                {
                    Transform dynamicHanging = hangingBoneRef.Find(CCameraTools.GetHangingStructuredName(context, hangingInfo.baseObjName, hangingInfo.offset, true));
                    if (!dynamicHanging)
                    {
                        GameObject go = new GameObject(CCameraTools.GetHangingStructuredName(context, hangingInfo.baseObjName, hangingInfo.offset, true));
                        go.transform.parent = hangingBoneRef;
                        go.transform.localPosition = new Vector3(Convert.ToSingle(hangingInfo.offset), 0f, 0f);
                        go.transform.localRotation = Quaternion.identity;
                        go.transform.localScale = Vector3.one;
                        dynamicHanging = go.transform;
                        actorSlots[context.targetIndex].dynamicHangings.Add(dynamicHanging);
                    }
                    return new HangingWithRelevantObjects()
                    {
                        hangings = dynamicHanging,
                        relevantObjects = new List<GameObject>() { dynamicHanging.gameObject }
                    };
                }
            }
            return null;
        }

        private HangingWithRelevantObjects GetDynamicHangingOnInitPose(HangingInfoStringOffsetByName hangingInfo, int slotIndex)
        {
            if (actorSlots[slotIndex] == null || actorSlots[slotIndex].initBoneTree == null)
                return null;

            HangingBindContext context = new HangingBindContext() { hangingTargetType = HangingTargetType.InitPose, targetIndex = slotIndex };
            string targetHangingName = CCameraTools.GetHangingStructuredName(
                context,
                hangingInfo.baseObjName,
                hangingInfo.offset,
                true);
            if (!string.IsNullOrEmpty(targetHangingName))
            {
                List<Transform> hangings = actorSlots[slotIndex].initPoseHanging.Where(x => x.name == targetHangingName).ToList();
                if (hangings.Count > 0)
                    return new HangingWithRelevantObjects()
                    {
                        hangings = hangings[0],
                        relevantObjects = new List<GameObject>() { hangings[0].gameObject }
                    };
                
                Transform hangingBoneRef = null;
                if (actorSlots[slotIndex].initBoneCacheMap.ContainsKey(hangingInfo.baseObjName))
                    hangingBoneRef = actorSlots[slotIndex].initBoneCacheMap[hangingInfo.baseObjName];
                if (hangingBoneRef == null)
                {
                    if (!string.IsNullOrEmpty(hangingInfo.baseObjName))
                    {
                        var hangingBoneNameMap = CfgManager.tables.TbCEditorHangingBoneName.Get(hangingInfo.baseObjName);
                        if (hangingBoneNameMap != null)
                        {
                            //string boneName = StationTemplateDataCollector.boneNameTrans[hangingInfo.baseObjName];
                            string boneName = hangingBoneNameMap.BoneName;
                            hangingBoneRef = FindChildInTransform(actorSlots[slotIndex].initBoneTree.transform, boneName, new List<string>());
                            if (hangingBoneRef != null)
                                actorSlots[slotIndex].initBoneCacheMap.Add(hangingInfo.baseObjName, hangingBoneRef);
                        }
                    }
                }
                if (hangingBoneRef != null)
                {
                    Transform initPoseHanging = hangingBoneRef.Find(CCameraTools.GetHangingStructuredName(context, hangingInfo.baseObjName, hangingInfo.offset, true));
                    if (!initPoseHanging)
                    {
                        GameObject go = new GameObject(CCameraTools.GetHangingStructuredName(context, hangingInfo.baseObjName, hangingInfo.offset, true));
                        go.transform.parent = hangingBoneRef;
                        go.transform.localPosition = new Vector3(Convert.ToSingle(hangingInfo.offset), 0f, 0f);
                        go.transform.localRotation = Quaternion.identity;
                        go.transform.localScale = Vector3.one;
                        initPoseHanging = go.transform;
                        actorSlots[context.targetIndex].initPoseHanging.Add(initPoseHanging);
                    }
                    return new HangingWithRelevantObjects()
                    {
                        hangings = initPoseHanging,
                        relevantObjects = new List<GameObject>() { initPoseHanging.gameObject }
                    };
                }
            }
            return null;
        }

        private HangingWithRelevantObjects GetHangingWithPerformType(HangingInfoStringOffsetByName hangingInfo, int slotIndex, HangingPerformType hangingPerform)
        {
            switch (hangingPerform)
            {
                case HangingPerformType.Dynamic:
                    return GetDynamicHangingOnActor(hangingInfo, slotIndex);
                case HangingPerformType.StaticReferCurrentPose:
                    return GetDynamicHangingOnActor(hangingInfo, slotIndex);
                case HangingPerformType.StaticReferInitPose:
                    return GetDynamicHangingOnInitPose(hangingInfo, slotIndex);
                default: return null;
            }
        }

        private HangingWithRelevantObjects GenerateTargetGroup(string targetGroupName, CameraGroupOfTargetSlotInfo cameraGroupInfo, HangingPerformType hangingPerform)
        {
            List<TargetGroupHangingInfoStringOffsetByName> hangingList = CCameraTools.AnalysisTargetGroupName(targetGroupName);
            List<GameObject> relevantList = new List<GameObject>();
            for (int i = 0; i < hangingList.Count; i++)
                hangingList[i].slotIndex = cameraGroupInfo.targetSlotIndex[hangingList[i].slotIndex];
            GameObject go = new GameObject(CCameraTools.GetUniqueGameObjectName(targetGroupName, getTargetGroup().transform));
            CinemachineTargetGroup cinemachineTargetGroup = go.AddComponent<CinemachineTargetGroup>();
            relevantList.Add(go);
            for (int i = 0;i < hangingList.Count; i++)
            {
                HangingWithRelevantObjects memberTrans = GetHangingWithPerformType(new HangingInfoStringOffsetByName()
                {
                    baseObjName = hangingList[i].baseObjName,
                    offset = hangingList[i].offset,
                    type = hangingList[i].type
                }, 
                hangingList[i].slotIndex,
                hangingPerform);

                if (memberTrans == null)
                    memberTrans = new HangingWithRelevantObjects();
                if (memberTrans.hangings == null)
                    memberTrans.hangings = actorSlots[hangingList[i].slotIndex].slot.transform;

                cinemachineTargetGroup.AddMember(memberTrans.hangings, 1, 0);
                relevantList.Add(memberTrans.hangings.gameObject);
            }

            go.transform.parent = getTargetGroup().transform;

            cinemachineTargetGroup.DoUpdate();

            return new HangingWithRelevantObjects() 
            {
                hangings = cinemachineTargetGroup.transform,
                relevantObjects = relevantList
            };
        }


        public HangingWithRelevantObjects GetHangingTarget(HangingInfoStringOffsetByName hangingInfo, CameraGroupOfTargetSlotInfo cameraGroupInfo, Transform belongingSubCameraGroup, HangingPerformType hangingPerform)
        {
            switch (hangingInfo.type)
            {
                case HangingType.ActorBaseHanging:
                    return GetHangingWithPerformType(hangingInfo, cameraGroupInfo.targetSlotIndex[0], hangingPerform);
                case HangingType.TargetGroup:
                    return GenerateTargetGroup(hangingInfo.baseObjName, cameraGroupInfo, hangingPerform);
                case HangingType.FixedPoint:
                    if (belongingSubCameraGroup != null)
                    {
                        Transform followGroup = belongingSubCameraGroup.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.FixFollowPoint]);
                        if (followGroup != null)
                        {
                            Transform fixFollowTarget = followGroup.Find(hangingInfo.baseObjName);
                            if (fixFollowTarget != null)
                            {
                                return new HangingWithRelevantObjects()
                                {
                                    hangings = fixFollowTarget,
                                    relevantObjects = new List<GameObject>() { fixFollowTarget.gameObject }
                                };
                            }
                        }
                    }
                    return null;
                default:
                    return null;
            }
        }

        private void SyncCameraState(bool manualRender = false)
        {
            if (mainCamera == null || brain == null)return;
            if (brain.ActiveVirtualCamera == null) return;
            if (brain.ActiveVirtualCamera.VirtualCameraGameObject == null) return;

            mainCamera.transform.position = brain.transform.position;
            mainCamera.transform.rotation = brain.transform.rotation;
            mainCamera.transform.localScale = brain.transform.localScale;

            mainCamera.fieldOfView = brain.CurrentCameraState.Lens.FieldOfView;
            mainCamera.orthographicSize = brain.CurrentCameraState.Lens.OrthographicSize;
            mainCamera.nearClipPlane = brain.CurrentCameraState.Lens.NearClipPlane;
            mainCamera.farClipPlane = brain.CurrentCameraState.Lens.FarClipPlane;
            mainCamera.lensShift = brain.CurrentCameraState.Lens.LensShift;
            mainCamera.gateFit = brain.CurrentCameraState.Lens.GateFit;

            if (manualRender)
                mainCamera.Render();
        }

        public static Transform FindChildInTransform(Transform parent, string child, List<string> forbiddens)
        {
            if (forbiddens.Contains(parent.gameObject.name))
                return null;
            if (parent.gameObject.name == child)
                return parent;

            Transform childTF = parent.Find(child);
            if (childTF != null)
            {
                return childTF;
            }
            for (int i = 0; i < parent.childCount; i++)
            {
                if (forbiddens.Contains(parent.GetChild(i).name))
                    continue;
                childTF = FindChildInTransform(parent.GetChild(i), child, forbiddens);
                if (childTF != null)
                    return childTF;
            }
            return null;
        }

        private void StandardFaceMeshPath(Transform charactorRoot)
        {
            if (charactorRoot == null)
                return;
            for (int i = 0; i < charactorRoot.childCount; i++)
            {
                if (charactorRoot.GetChild(i).name.Substring(0, 1) == "M")
                {
                    charactorRoot.GetChild(i).name = "Mesh_Lod0";
                    for (int j = 0; j < charactorRoot.GetChild(i).childCount; j++)
                    {
                        string[] tags = charactorRoot.GetChild(i).GetChild(j).name.Split("_");
                        if (tags[tags.Length - 1] == "Face")
                        {
                            charactorRoot.GetChild(i).GetChild(j).name = "Mesh_Face";
                            break;
                        }
                    }
                    break;
                }
            }
        }

        private void ShowSlotUI()
        {
#if UNITY_EDITOR
            if (actorSlots != null && actorSlots.Count > 0)
            {
                for (int i = 0; i < actorSlots.Count; i++)
                {
                    if (actorSlots[i].actor == null)
                    {
                        Transform textMeshHolder = actorSlots[i].slot.transform.Find("Text");
                        if (textMeshHolder == null)
                        {
                            textMeshHolder = new GameObject("Text").transform;
                            TextMesh textMesh = textMeshHolder.gameObject.AddComponent<TextMesh>();
                            textMesh.transform.parent = actorSlots[i].slot.transform;
                            textMesh.transform.localPosition = new Vector3(0, 0.3f, 0);
                            textMesh.transform.localScale = new Vector3(0.03f, 0.03f, 0.03f);
                            textMesh.transform.localRotation = Quaternion.identity;
                            textMesh.text = StationTemplateToolsKit.GetSlotStringNameByIndex(i);
                            textMesh.fontSize = 100;
                            textMesh.color = Color.red;
                            textMesh.anchor = TextAnchor.MiddleCenter;
                            textMesh.characterSize = 1.0f;
                            textMesh.lineSpacing = 1.0f;
                            textMesh.richText = false;
                        }
                        textMeshHolder.rotation = Quaternion.LookRotation(textMeshHolder.transform.position - SceneView.lastActiveSceneView.camera.transform.position);
                    }
                    else
                    {
                        if (actorSlots[i].slot.transform.Find("Text") != null)
                            DestroyImmediate(actorSlots[i].slot.transform.Find("Text"));
                    }
                }
            }
#endif
        }

        public Transform GetLookAtBodyTarget(int slotIndex, LookAtBodyTarget lookAtBodyTarget)
        {
            if (actorSlots != null && slotIndex >= 0 && slotIndex < actorSlots.Count)
            {
                if (actorSlots[slotIndex].actor != null)
                {
                    return FindChildInTransform(
                        actorSlots[slotIndex].actor.transform, 
                        LookAtDataCollector.lookAtBodyBoneName[lookAtBodyTarget], 
                        new List<string>() { 
                            StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand], 
                            StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit] });
                }
            }
            return null;
        }


        // 相关获取函数
        public Camera GetMainCamera() 
        {
            if (mainCamera == null)
                return null;
            return mainCamera; 
        }

        public CinemachineBrain GetCinemachineBrain()
        {
            if (brain == null)
                return null;
            return brain;
        }

        public GameObject getCameraGroup()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
            {
                Transform group = stationTemplate.stationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CameraGroup]);
                if (group != null)
                    return group.gameObject;
            }
            GameObject go = getStationTemplate();
            if (go != null)
                return go.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CameraGroup]).gameObject;
            return null;
        }

        public GameObject getActorGroup()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
            {
                Transform group = stationTemplate.stationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.ActorGroup]);
                if (group != null)
                    return group.gameObject;
            }
            GameObject go = getStationTemplate();
            if (go != null)
                return go.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.ActorGroup]).gameObject;
            return null;
        }

        public GameObject getHangingGroup()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
            {
                Transform group = stationTemplate.stationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.HangingGroup]);
                if (group != null)
                    return group.gameObject;
            }
            GameObject go = getStationTemplate();
            if (go != null)
                return go.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.HangingGroup]).gameObject;
            return null;
        }

        public GameObject getCloneCameraGroup()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
            {
                Transform group = stationTemplate.stationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CloneCameraGroup]);
                if (group != null)
                    return group.gameObject;
            }
            GameObject go = getStationTemplate();
            if (go != null)
                return go.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CloneCameraGroup]).gameObject;
            return null;
        }

        public GameObject getTargetGroup()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
            {
                Transform group = stationTemplate.stationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.TargetGroup]);
                if (group != null)
                    return group.gameObject;
            }
            GameObject go = getStationTemplate();
            if (go != null)
                return go.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.TargetGroup]).gameObject;
            return null;
        }

        public GameObject getDerCamPawnGroup()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
            {
                Transform group = stationTemplate.stationTemplate.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.DerCamPawnGroup]);
                if (group != null)
                    return group.gameObject;
            }
            GameObject go = getStationTemplate();
            if (go != null)
                return go.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.TargetGroup]).gameObject;
            return null;
        }

        public GameObject getStationTemplate()
        {
            if (stationTemplate != null && stationTemplate.stationTemplate != null)
                return stationTemplate.stationTemplate;

            if (this.gameObject.transform.childCount > 0)
            {
                for (int i = 0; i < this.gameObject.transform.childCount; i++)
                {
                    if (this.gameObject.transform.GetChild(i).Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.ActorGroup]) != null &&
                        this.gameObject.transform.GetChild(i).Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.CameraGroup]) != null)
                    {
                        return this.gameObject.transform.GetChild(i).gameObject;
                    }
                }
            }
            return null;
        }

        public int GetActorIndexByGameObject(GameObject actor)
        {
            if (actorSlots == null || actorSlots.Count == 0)
                return -1;

            int index = -1;
            for (int i = 0; i < actorSlots.Count; i++)
            {
                if (actorSlots[i].actor == null) continue;
                if (object.ReferenceEquals(actor, actorSlots[i].actor))
                    return i;
            }
            return index;
        }

        /// <summary>
        /// 使用默认填充的角色继承方式进行切换站位模板
        /// </summary>
        /// <param name="templateID"></param>
        public async void SetStationTemplateID(int templateID)
        {
            var cfg = CfgManager.tables.TbCEditorStationTemplate.GetOrDefault(templateID);
            if (cfg != null && !string.IsNullOrEmpty(cfg.FileName))
            {
                var stationTemplatePrefab =  await DialogueUtil.Load<GameObject>(cfg.FileName);
                if (stationTemplatePrefab != null)
                {
                    GameObject templateObject = Instantiate(stationTemplatePrefab);
                    if (templateObject != null)
                    {
                        templateObject.name = stationTemplatePrefab.name;
                        
                        StationTemplateType type = StationTemplateToolsKit.GetStationTemplateTypeFromName(cfg.Type);
                        SignUpStationTemplate(templateObject, templateID, type);
                        if (brain == null)
                        {
                            InitCameraBrain();
                            ReleaseCameraBrainAbnormalState();
                        }
                        return;
                    }
                }
            }
            Debug.LogWarning(ZString.Concat("templateID ", templateID.ToString(), " is NOT Valid"));
        }

        /// <summary>
        /// 通过ID，设置新的站位模板，可通过switchMode选择切换站位模板时角色槽中角色的继承模式
        /// </summary>
        /// <param name="templateID"></param>
        /// <param name="switchMode"></param>
        public async void SetStationTemplateID(int templateID, SwitchStationTemplateMode switchMode)
        {
            var cfg = CfgManager.tables.TbCEditorStationTemplate.GetOrDefault(templateID);
            if (cfg != null && !string.IsNullOrEmpty(cfg.FileName))
            {
                var stationTemplatePrefab = await DialogueUtil.Load<GameObject>(cfg.FileName);
                if (stationTemplatePrefab != null)
                {
                    GameObject templateObject = Instantiate(stationTemplatePrefab);
                    if (templateObject != null)
                    {
                        templateObject.name = stationTemplatePrefab.name;
                        StationTemplateType type = StationTemplateToolsKit.GetStationTemplateTypeFromName(cfg.Type);
                        SignUpStationTemplate(templateObject, templateID, type, switchMode);
                        if (brain == null)
                        {
                            InitCameraBrain();
                            ReleaseCameraBrainAbnormalState();
                        }
                        return;
                    }
                }
            }
            Debug.LogWarning(ZString.Concat("templateID ", templateID.ToString(), " is NOT Valid"));
        }

        public void SetLastCutsceneEndCamPosition(Vector3 pos, Quaternion rot, float Fov)
        {
            Debug.Log(ZString.Concat("SetLastCutsceneEndCamPosition ", pos.ToString(), " ", rot.ToString(), " ", Fov.ToString()));
            prevCutsceneEndCamPosition = pos;
            prevCutsceneEndCamRotation = rot;
            prevCutsceneEndCamFOV = Fov;
        }

        /// <summary>
        /// 设置所以槽位的角色信息
        /// </summary>
        /// <param name="actorsInfoList">角色列表</param>
        public void SetCharacterSlots(List<ActorSlotSignUpInfo> actorsInfoList, bool prepareBlendToPlayable)
        {
            if (actorsInfoList == null){
                Debug.Log("actorInfoList is null");
                return;}

            for (int i = 0; i < actorsInfoList.Count; i++)
            {
                if (actorsInfoList[i] != null && actorsInfoList[i].slotIndex < actorSlots.Count && actorsInfoList[i].slotIndex >= 0)
                    SwitchActor(actorsInfoList[i].slotIndex, actorsInfoList[i].go, actorsInfoList[i].cfg, actorsInfoList[i].lookAtCfg, prepareBlendToPlayable);
                else
                    Debug.LogWarning(ZString.Concat("ActorIndex ", actorsInfoList[i].slotIndex.ToString(), " Not Vaild"));
            }
        }
        
        /// <summary>
        /// 设置某一单一槽位的角色信息
        /// </summary>
        /// <param name="actorInfo">单一角色</param>
        public void SetCharacterSlot(ActorSlotSignUpInfo actorInfo, bool prepareBlendToPlayable)
        {
            if (actorInfo != null && actorInfo.slotIndex < actorSlots.Count && actorInfo.slotIndex >= 0)
                SwitchActor(actorInfo.slotIndex, actorInfo.go, actorInfo.cfg, actorInfo.lookAtCfg, prepareBlendToPlayable);
            else
                Debug.LogWarning(ZString.Concat("ActorIndex ", actorInfo.slotIndex.ToString(), " Not Vaild"));
        }


        private bool CheckIsTemplateFirstCam()
        {
            Debug.Log("CheckIsTemplateFirstCam");
            if (stationTemplate.ID != historyStationTemplateID || Vector3.Distance(stationTemplate.stationTemplate.transform.position, historyStationTemplatePosition) > 0.001 || MathF.Abs(Quaternion.Angle(stationTemplate.stationTemplate.transform.rotation, historyStationTemplateRotation)) > 0.001)
            {
                Debug.Log("CheckIsTemplateFirstCam True");
                historyStationTemplateID = stationTemplate.ID;
                historyStationTemplatePosition = stationTemplate.stationTemplate.transform.position;
                historyStationTemplateRotation = stationTemplate.stationTemplate.transform.rotation;
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 基于镜头ID和镜头目标类别，切到一个单一镜头
        /// </summary>
        /// <param name="camParam"></param>
        public void SetCameraID(CameraIDTransParam camParam, string targetTag, bool filterWeakLensChanges)
        {
            if (camParam == null)
                return;
            if (CheckCameraTargetValid(targetTag) == false)
                return;
            CameraRefTransParam camRefParam = TransCameraIDAndTargetTagToCameraRefParam(camParam, targetTag, true);
            if (camRefParam != null)
            {
                if (CheckIsTemplateFirstCam())
                {
                    camRefParam.cameraPerformConfig.cameraSwitchPerform.blendTime = 0;
                    camRefParam.cameraPerformConfig.cameraSwitchPerform.blendStyle = CinemachineBlendDefinition.Style.Cut;
                }
                if (cameraSwitchSystem != null)
                {
                    if (camRefParam.cameraPerformConfig.followPerform == HangingPerformType.Dynamic || camRefParam.cameraPerformConfig.lookAtPerform == HangingPerformType.Dynamic)
                        filterWeakLensChanges = false;
                    if (!useRootMotion)
                    {
                        alignActorRotateState = new AlignActorRotateState
                        {
                            camName = camRefParam.cam.name,
                            targetTag = targetTag,
                            life = 0
                        };
                    }
                    singleCamChangeState = new SingleCamChangeState
                    {
                        camRefTransParam = camRefParam,
                        targetTag = targetTag,
                        checkSimilar = filterWeakLensChanges,
                        life = 0
                    };
                    //cameraSwitchSystem.ChangeSingleCamera(camRefParam, targetTag);
                }
            }
            else
                Debug.LogWarning("Camera Info Wrong");
        }

        public void SetBlendPrevCutsceneCamCamera(CameraIDTransParam camParam, string targetTag)
        {
            Debug.Log("SetBlendPrevCutsceneCamCamera");
            if (camParam == null)
                return;
            if (CheckCameraTargetValid(targetTag) == false)
                return;
            CameraRefTransParam camRefParam = TransCameraIDAndTargetTagToCameraRefParam(camParam, targetTag, false);
            if (camRefParam != null)
            {
                if (cameraSwitchSystem != null)
                {
                    cameraSwitchSystem.ChangeAbsCamera(
                        new CameraSwitchPerformConfig { blendStyle = CinemachineBlendDefinition.Style.Cut, blendTime = 0, custromBlendCurve = null}, 
                        prevCutsceneEndCamPosition, 
                        prevCutsceneEndCamRotation.eulerAngles, 
                        prevCutsceneEndCamFOV);
                    SyncCameraState(true);

                    if (!useRootMotion)
                    {
                        alignActorRotateState = new AlignActorRotateState
                        {
                            camName = camRefParam.cam.name,
                            targetTag = targetTag,
                            life = 0
                        };
                    }
                    singleCamWithAbsCamBlendState = new SingleCamWithAbsCamBlendState
                    {
                        camRefTransParam = camRefParam,
                        targetTag = targetTag,
                        absCamWorldPos = prevCutsceneEndCamPosition,
                        absCamWorldRot = prevCutsceneEndCamRotation.eulerAngles,
                        absCamFOV = prevCutsceneEndCamFOV,
                        life = 0
                    };
                    //cameraSwitchSystem.ChangeSingleCamera(camRefParam, targetTag);
                }
            }
            else
                Debug.LogWarning("Camera Info Wrong");
        }

        public void SetAnimatedCamera(CameraSwitchPerformConfig cameraSwitchPerform, AnimationClip camAnim)
        {
            if (cameraSwitchSystem != null)
            {
                if (CheckIsTemplateFirstCam())
                {
                    cameraSwitchPerform.blendTime = 0;
                    cameraSwitchPerform.blendStyle = CinemachineBlendDefinition.Style.Cut;
                }
                cameraSwitchSystem.ChangeAnimatedCamera(cameraSwitchPerform, camAnim);
                SyncCameraState(true);
            }
        }

        public void SetAbsCamera(CameraSwitchPerformConfig cameraSwitchPerform, Vector3 absCamWorldPos, Vector3 absCamWorldRot, float absCamFOV)
        {
            if (cameraSwitchSystem != null)
            {
                if (CheckIsTemplateFirstCam())
                {
                    cameraSwitchPerform.blendTime = 0;
                    cameraSwitchPerform.blendStyle = CinemachineBlendDefinition.Style.Cut;
                }
                cameraSwitchSystem.ChangeAbsCamera(cameraSwitchPerform, absCamWorldPos, absCamWorldRot, absCamFOV);
                SyncCameraState(true);
            }
        }

        /// <summary>
        /// 切到一个运镜镜头
        /// </summary>
        /// <param name="camGroupParam"></param>
        /// <param name="targetTag"></param>
        public void SetCameraGroupID(CameraGroupIDTransParam camGroupParam, string targetTag, bool filterWeakLensChanges)
        {
            if (camGroupParam == null)
                return;
            CameraGroupRefTransParam cameraGroupRefTransParam = new CameraGroupRefTransParam();
            cameraGroupRefTransParam.camGroup = new List<CameraRefTransParam>();
            cameraGroupRefTransParam.camGroupTransInPerform = camGroupParam.camGroupTransInPerform;
            for(int i = 0; i < camGroupParam.camGroup.Count; i++)
            {
                if (camGroupParam.camGroup[i] == null)
                    return;
                if (CheckCameraTargetValid(targetTag) == false)
                    return;
                CameraRefTransParam camRefParam = TransCameraIDAndTargetTagToCameraRefParam(camGroupParam.camGroup[i], targetTag, true);
                if (camRefParam != null){
                    if (i == 0)
                    {
                        if (CheckIsTemplateFirstCam())
                        {
                            camRefParam.cameraPerformConfig.cameraSwitchPerform.blendTime = 0;
                            camRefParam.cameraPerformConfig.cameraSwitchPerform.blendStyle = CinemachineBlendDefinition.Style.Cut;
                        }
                    }
                    cameraGroupRefTransParam.camGroup.Add(camRefParam);
                }else{
                    Debug.LogWarning("Camera Info Wrong");
                    return;
                }
            }
            if (cameraSwitchSystem != null && cameraGroupRefTransParam.camGroup.Count > 0)
            {
                if (!useRootMotion)
                {
                    alignActorRotateState = new AlignActorRotateState
                    {
                        camName = cameraGroupRefTransParam.camGroup[0].cam.name,
                        targetTag = targetTag,
                        life = 0
                    };
                }
                groupCamChangeState = new GroupCamChangeState
                {
                    camGroupRefTransParam = cameraGroupRefTransParam,
                    targetTag = targetTag,
                    checkSimilar = filterWeakLensChanges,
                    life = 0
                };
                //cameraSwitchSystem.ChangeBlendListCamera(cameraGroupRefTransParam, targetTag);
            }
        }


        /// <summary>
        /// 赋予主镜头引用
        /// </summary>
        /// <param name="cam"></param>
        public void SetCameraRef(Camera cam)
        {
            instance.mainCamera = cam;
            InitMainCamParam();
        }

        public void SetStationTemplateOriginalTransform(Transform inTransform)
        {
            gameObject.transform.position = inTransform.position;
            gameObject.transform.rotation = inTransform.rotation;
        }
        
        public void SetStationTemplateOriginalTransform(Vector3 position, Quaternion rotation)
        {
            gameObject.transform.position = position;
            gameObject.transform.rotation = rotation;
        }
        

        /// <summary>
        /// 基于站位，返回所有可选的镜头目标组类型
        /// 如三人模板，返回一个三人镜头目标组和多个单双人镜头目标组，"A","B","C","AB","AC","ABC" 
        /// </summary>
        public List<string> GetTargetSlotsList()
        {
            if (targetSlotCameraGroups == null)
                return null;
            List<string> targetGroupList = targetSlotCameraGroups.Select(x => x.targetSlotGroupTag).ToList();
            return targetGroupList;
        }

        public static List<CameraGroupOfTargetSlotInfo> GetTargetSlotsListV3(int templateID = 10000)
        {
            var baseCfg = CfgManager.tables.TbCEditorStationTemplate.GetOrDefault(templateID);
            if (baseCfg == null)
            {
                Log.LogWarning("templateID={0} NOT found in GetTargetSlotsListV2", templateID);
                return null;
            }
            int slotCount = StationTemplateToolsKit.GetSlotCountByStationTemplateName(baseCfg.Type);

            List<CameraGroupOfTargetSlotInfo> staticTargetSlotCameraGroups = new List<CameraGroupOfTargetSlotInfo>();

            // 注册基础模板
            List<int> fullIndexList = new List<int>();
            for (int i = 0; i < slotCount; i++)
                fullIndexList.Add(i);
            string defaultTargetSlotGroupTag = StationTemplateToolsKit.GetTargetTag(fullIndexList);
            staticTargetSlotCameraGroups.Add(new CameraGroupOfTargetSlotInfo()
            {
                targetSlotGroupTag = defaultTargetSlotGroupTag,
                targetSlotIndex = fullIndexList,
                templateID = templateID,
                groupGameObject = null,
                spawnParentGameObject = null
            });
            // 获取衍生镜头list
            List<List<int>> derGroupSlotTarget = StationTemplateToolsKit.GetAllSubset(slotCount);
            List<List<int>> garbage = new List<List<int>>();
            foreach (List<int> ts in derGroupSlotTarget)
            {
                if (staticTargetSlotCameraGroups.Find(x => x.targetSlotGroupTag == StationTemplateToolsKit.GetTargetTag(ts)) != null)
                    garbage.Add(ts);
            }
            foreach (var g in garbage)
                derGroupSlotTarget.Remove(g);

            // 计算真实衍生镜头list
            if (derGroupSlotTarget.Count > 0)
            {
                string derCamGroupTag = baseCfg.DerTags;
                List<DerivativeCameraGroupInfo> derCamGroupInfos = new List<DerivativeCameraGroupInfo>();
                if (derCamGroupTag != null)
                {
                    // 注册默认衍生镜头
                    List<DerivativeCameraGroupInfo> defaultDerCamsInfo = StationTemplateToolsKit.GetDefaultDerCamGroupIDs(derCamGroupTag);
                    if (defaultDerCamsInfo != null)
                    {
                        foreach (var d in defaultDerCamsInfo)
                        {
                            var derCfg = CfgManager.tables.TbCEditorStationTemplate.Get(d.templateID);
                            if (derCfg != null && !string.IsNullOrEmpty(derCfg.FileName))
                            {
                                derCamGroupInfos.Add(new DerivativeCameraGroupInfo()
                                {
                                    templateID = d.templateID,
                                    slotNum = StationTemplateToolsKit.GetSlotCountByStationTemplateName(derCfg.Type),
                                    subGroupObject = null
                                });
                            }
                        }
                    }
                    // 注册虚拟衍生镜头组
                    foreach (List<int> derGroup in derGroupSlotTarget)
                    {
                        DerivativeCameraGroupInfo derCameraGroup = derCamGroupInfos.Find(x => x.slotNum == derGroup.Count);
                        string targetSlotTag = StationTemplateToolsKit.GetTargetTag(derGroup);
                        staticTargetSlotCameraGroups.Add(new CameraGroupOfTargetSlotInfo()
                        {
                            targetSlotGroupTag = targetSlotTag,
                            targetSlotIndex = derGroup,
                            templateID = derCameraGroup == null ? -1 : derCameraGroup.templateID,
                            groupGameObject = null,
                            spawnParentGameObject = null
                        });
                    }
                    // 注册覆写镜头组
                    List<CameraGroupOfTargetSlotInfo> overwriteCamGroupsInfo = StationTemplateToolsKit.GetOverwriteDerCamGroupInfo(derCamGroupTag);
                    if (overwriteCamGroupsInfo != null)
                    {
                        foreach (var o in overwriteCamGroupsInfo)
                        {
                            CameraGroupOfTargetSlotInfo defaultInfo = staticTargetSlotCameraGroups.Find(x => x.targetSlotGroupTag == o.targetSlotGroupTag);
                            if (defaultInfo != null)
                            {
                                if (o.templateID == -1)
                                    defaultInfo.templateID = -1;
                                else
                                    defaultInfo.templateID = o.templateID;
                            }
                        }
                    }
                    // 加载覆写镜头镜头组并完善targetSlotCameraGroups相关信息
                    foreach (var t in staticTargetSlotCameraGroups)
                    {
                        if (t == null) continue;
                        if (t.templateID != -1)
                        {
                            DerivativeCameraGroupInfo derCamInfo = derCamGroupInfos.Find(x => x.templateID == t.templateID);
                            if (derCamInfo == null)
                            {
                                var derCfg = CfgManager.tables.TbCEditorStationTemplate.Get(t.templateID);
                                if (derCfg != null && !string.IsNullOrEmpty(derCfg.FileName))
                                {
                                    derCamGroupInfos.Add(new DerivativeCameraGroupInfo()
                                    {
                                        templateID = t.templateID,
                                        slotNum = StationTemplateToolsKit.GetSlotCountByStationTemplateName(derCfg.Type),
                                        subGroupObject = null
                                    });
                                }
                                else
                                {
                                    t.templateID = -1;
                                }
                            }
                        }
                    }
                    // 清理无镜头组targetTag
                    List<CameraGroupOfTargetSlotInfo> targetTagGarbage = new List<CameraGroupOfTargetSlotInfo>();
                    foreach (var t in staticTargetSlotCameraGroups)
                        if (t.templateID == -1)
                            targetTagGarbage.Add(t);
                    foreach (var g in targetTagGarbage)
                        staticTargetSlotCameraGroups.Remove(g);
                }
            }
            return staticTargetSlotCameraGroups;
        }

        /// <summary>
        /// 清除所有manager当前状态，但会保留角色实例和最后一个相机的所有状态
        /// </summary>
        public void Clear()
        {
            for (int i = 0; i < actorSlots.Count; i++)
            {
                CleanUpActorInSlotSoft(i, true, false, false, false, false, false, false);
            }
            HoldCameraStateBeforClear();
            CleanUpRuntimeDataRelevantToStationTemplate();
#if UNITY_EDITOR
            for (int i = 0; i < transform.childCount; i++)
                DestroyImmediate(transform.GetChild(i).gameObject);
#else
            for (int i = 0; i < transform.childCount; i++)
                Destroy(transform.GetChild(i).gameObject);
#endif
        }

        /// <summary>
        /// 清除某一个槽位上与manager相关的信息，保留角色实例和C类组件，维护相机的状态
        /// </summary>
        public void ClearSlot(int slotIndex)
        {
            if (slotIndex < actorSlots.Count && slotIndex >= 0)
            {
                CleanUpActorInSlotSoft(slotIndex, true, false, false, false, false, false, false);
            }
        }

        /// <summary>
        /// 清除某一个槽位上与manager相关的信息，保留角色实例，清除C类组件，维护相机的状态
        /// </summary>
        public void ClearSlotHard(int slotIndex)
        {
            if (slotIndex < actorSlots.Count && slotIndex >= 0)
            {
                CleanUpActorInSlotSoft(slotIndex, true, true, true, true, true, true, true);
            }
        }

        /// <summary>
        /// 清除所有manager当前状态，保留角色实例，但不会维护最后一个相机的状态
        /// </summary>
        public void CompletelyClear()
        {
            for (int i = 0; i < actorSlots.Count; i++)
            {
                CleanUpActorInSlotSoft(i, false, true, true, true, true, true, true);
            }
            CleanUpRuntimeDataRelevantToStationTemplate();
            if (cloneCameraBuffer != null)
                cloneCameraBuffer.Clear();
#if UNITY_EDITOR
            for (int i = 0; i < transform.childCount; i++)
                DestroyImmediate(transform.GetChild(i).gameObject);
#else
            for (int i = 0; i < transform.childCount; i++)
                Destroy(transform.GetChild(i).gameObject);
#endif
        }

        public void LateToAutoDestroyStationTemplate()
        {
            lateCompleteClearStationTemplate = true;
        }

        private void CheckAutoDestroyStationTemplate()
        {
            if (!lateCompleteClearStationTemplate)
                return;

            if (actorSlots == null || cloneCameraBuffer == null)
                CompletelyClear();

            bool actorReadyToDestroy = true;
            foreach (var slot in actorSlots)
                if (slot.actor != null)
                    actorReadyToDestroy = false;

            bool cameraReadyToDestroy = true;

            if (actorReadyToDestroy && cameraReadyToDestroy)
            {
                CompletelyClear();
                lateCompleteClearStationTemplate = false;
            }
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}