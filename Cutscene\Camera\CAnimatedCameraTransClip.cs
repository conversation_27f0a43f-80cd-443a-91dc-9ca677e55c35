using System.Collections;
using Cinemachine;
using Sirenix.OdinInspector;
using UnityEngine;
using CEditor;
using System.Collections.Generic;
using System.Linq;
using VGame.Framework;
using System;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.UIElements;
using VGame;
using ET;

namespace Slate.ActionClips
{
    [Name("动画镜头配置")]
    [Attachable(typeof(CCameraTransTrack))]
    [Category("C类编辑器")]
    public class CAnimatedCameraTransClip : DirectorActionClip
    {
        [SerializeField, LabelText("镜头挂点"), ValueDropdown("CamPickerConfig")]
        public string CamPickerName = "";

        private IEnumerable CamPickerConfig()
        {
            Cutscene cutscene = root as Cutscene;
            if (cutscene == null)
                return null;
            if (cutscene.StoryScene == null)
                return null;
            var stationTemplatePickers = cutscene.StoryScene.GetComponentsInChildren<StationTemplatePicker>();
            List<string> pickerNames;
            pickerNames = stationTemplatePickers.Select(x => x.name).ToList();
            return pickerNames;
        }

        [SerializeField, LabelText("镜头动画"), ValueDropdown("CameraAnimConfig"), OnValueChanged("OnCameraAnimChange")]
        public int cameraAnimKey;
        private IEnumerable CameraAnimConfig()
        {
            return CfgManager.tables.TbCEditorCameraAnimTable.DataList.Select(x =>
            {
                return new ValueDropdownItem($"{x.ID}-{x.Detail} [{x.FileName}]", x.ID);
            });
        }
        private AnimationClip _cameraAnimClip;
        public async ETTask<AnimationClip> GetCameraAnim()
        {
            if (_cameraAnimClip != null) return _cameraAnimClip;
            var cfg = CfgManager.tables.TbCEditorCameraAnimTable.GetOrDefault(cameraAnimKey);
            if (cfg != null && !string.IsNullOrEmpty(cfg.FileName))
            {
                _cameraAnimClip = await DialogueUtil.Load<AnimationClip>(cfg.FileName);
                return _cameraAnimClip;
            }
            return null;
        }
        public void OnCameraAnimChange()
        {
            _cameraAnimClip = null;
        }

        //[SerializeField, LabelText("镜头动画")]
        //public AnimationClip cameraAnimationClip;

        [SerializeField, LabelText("切镜过渡时长")]
        public float lensCutBlendTime = 0;

        [SerializeField, LabelText("切镜过渡曲线类别")]
        public CinemachineBlendDefinition.Style lensCutBlendType = CinemachineBlendDefinition.Style.Cut;

        [HideIf("@lensCutBlendType != CinemachineBlendDefinition.Style.Custom", 1)]
        [SerializeField, LabelText("切镜自定义过渡曲线")]
        public AnimationCurve lensCutCustomBlendCurve;
        
        public override string info
        {
            get
            {
                var asset = CfgManager.tables.TbCEditorCameraAnimTable.GetOrDefault(cameraAnimKey);
                if (asset != null)
                {
                    return asset.Detail;
                }
                else
                {
                    return "动画镜头配置";
                }
            }
        }

        //Called in forward sampling when the clip is entered
        protected override async ETTask OnEnterAsync()
        {
            //cameraSwitchSystem = performer.GetComponent<CameraSwitchSystem>();
            if (StationTemplateManager.instance != null)
            {
                var anim = await GetCameraAnim();
                if (anim != null)
                {
                    InitStationTemplateOriginalTransform();

                    CameraSwitchPerformConfig cameraSwitchPerform = new CameraSwitchPerformConfig();
                    cameraSwitchPerform.blendStyle = lensCutBlendType;
                    cameraSwitchPerform.custromBlendCurve = lensCutCustomBlendCurve;
                    cameraSwitchPerform.blendTime = lensCutBlendTime;

                    StationTemplateManager.instance.SetAnimatedCamera(cameraSwitchPerform, anim);
                }
            }
            
            Cutscene cutscene = root as Cutscene;
            if (TempUI.Instance != null && cutscene != null)
            {
                if(TempUI.Instance.GetMainCamera() != cutscene.camera)
                {
                    TempUI.Instance.ReloadMainCamera(cutscene.camera);
                    //Debug.Break();
                }
            }
        }

        void InitStationTemplateOriginalTransform()
        {
            if(!string.IsNullOrEmpty(CamPickerName))
            {
                Cutscene cutscene = root as Cutscene;
                if (cutscene != null && cutscene.StoryScene != null)
                {
                    var stationTemplatePickers = cutscene.StoryScene.GetComponentsInChildren<StationTemplatePicker>();
                    foreach (var picker in stationTemplatePickers)
                    {
                        if (picker.name.Equals(CamPickerName))
                        {
                            StationTemplateManager.instance.SetStationTemplateOriginalTransform(picker.transform);
                            break;
                        }
                    }
                }
            }
        }



        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {

        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}